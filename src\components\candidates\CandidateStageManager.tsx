import { useState } from 'react'
import { ChevronDown, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { useUpdateCandidateStage, type ICandidate } from '@/hooks/useCandidates'
import { useToast } from '@/hooks/use-toast'

interface CandidateStageManagerProps {
  candidate: ICandidate
  compact?: boolean
}

const stageOptions = [
  { 
    value: 'registered', 
    label: 'Registered', 
    color: 'bg-blue-100 text-blue-800',
    icon: Clock
  },
  { 
    value: 'veda-review', 
    label: 'Veda Review', 
    color: 'bg-purple-100 text-purple-800',
    icon: AlertCircle
  },
  { 
    value: 'screening', 
    label: 'Screening', 
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock
  },
  { 
    value: 'assessment', 
    label: 'Assessment', 
    color: 'bg-orange-100 text-orange-800',
    icon: Clock
  },
  { 
    value: 'interview', 
    label: 'Interview', 
    color: 'bg-indigo-100 text-indigo-800',
    icon: Clock
  },
  { 
    value: 'completed_success', 
    label: 'Completed - Success', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  { 
    value: 'completed_fail', 
    label: 'Completed - Failed', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
  { 
    value: 'workflow_terminated', 
    label: 'Workflow Terminated', 
    color: 'bg-gray-100 text-gray-800',
    icon: XCircle
  },
]

export function CandidateStageManager({ candidate, compact = false }: CandidateStageManagerProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const updateStageMutation = useUpdateCandidateStage()
  const { toast } = useToast()

  const currentStage = stageOptions.find(stage => stage.value === candidate.stage)
  const CurrentIcon = currentStage?.icon || Clock

  const handleStageUpdate = async (newStage: string) => {
    if (newStage === candidate.stage) return

    setIsUpdating(true)
    try {
      await updateStageMutation.mutateAsync({
        id: candidate._id,
        stage: newStage
      })
      
      const newStageOption = stageOptions.find(s => s.value === newStage)
      toast({
        title: "Stage Updated",
        description: `${candidate.name}'s stage updated to ${newStageOption?.label}`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update candidate stage",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
    }
  }

  if (compact) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger
          className="h-auto p-1 bg-transparent hover:bg-gray-100 rounded border-0 cursor-pointer flex items-center"
          disabled={isUpdating}
        >
          <Badge className={`${currentStage?.color || 'bg-gray-100 text-gray-800'} border-0 text-xs px-2 py-1`}>
            {currentStage?.label || candidate.stage}
          </Badge>
          <ChevronDown className="h-3 w-3 ml-1" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          {stageOptions.map((stage) => {
            const StageIcon = stage.icon
            const isCurrentStage = stage.value === candidate.stage
            
            return (
              <DropdownMenuItem
                key={stage.value}
                onClick={() => handleStageUpdate(stage.value)}
                disabled={isCurrentStage || isUpdating}
                className={isCurrentStage ? "bg-gray-50" : ""}
              >
                <StageIcon className="h-4 w-4 mr-2" />
                <span>{stage.label}</span>
                {isCurrentStage && (
                  <CheckCircle className="h-3 w-3 ml-auto text-green-500" />
                )}
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900">Current Stage</h4>
        <Badge className={`${currentStage?.color || 'bg-gray-100 text-gray-800'} border-0`}>
          <CurrentIcon className="h-3 w-3 mr-1" />
          {currentStage?.label || candidate.stage}
        </Badge>
      </div>
      
      <DropdownMenu>
        <DropdownMenuTrigger
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3 w-full justify-between"
          disabled={isUpdating}
        >
          Update Stage
          <ChevronDown className="h-4 w-4" />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <div className="px-2 py-1.5 text-xs font-medium text-gray-500 uppercase tracking-wide">
            Available Stages
          </div>
          <DropdownMenuSeparator />
          {stageOptions.map((stage) => {
            const StageIcon = stage.icon
            const isCurrentStage = stage.value === candidate.stage
            
            return (
              <DropdownMenuItem
                key={stage.value}
                onClick={() => handleStageUpdate(stage.value)}
                disabled={isCurrentStage || isUpdating}
                className={isCurrentStage ? "bg-gray-50" : ""}
              >
                <StageIcon className="h-4 w-4 mr-2" />
                <span>{stage.label}</span>
                {isCurrentStage && (
                  <CheckCircle className="h-3 w-3 ml-auto text-green-500" />
                )}
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
