import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { FileText, ExternalLink, Clock, Target, BookOpen, RotateCcw, Zap } from 'lucide-react'
import type { AssessmentTypesResponse } from '@/types'

interface ManualAssessmentConfigFormProps {
  form: UseFormReturn<any>
  assessmentTypes?: AssessmentTypesResponse
}

export function ManualAssessmentConfigForm({ form, assessmentTypes }: ManualAssessmentConfigFormProps) {
  const { register, watch, setValue, formState: { errors } } = form

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-green-600" />
          <span>Manual Assessment Configuration</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Fields */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
            <span>Required Information</span>
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="assessment-platform" className="flex items-center space-x-2">
                <ExternalLink className="h-4 w-4" />
                <span>Assessment Platform</span>
              </Label>
              <Input
                id="assessment-platform"
                {...register('manualAssessmentConfig.assessmentPlatform')}
                placeholder="e.g., HackerRank, CodeSignal, LeetCode"
              />
              {errors.manualAssessmentConfig?.assessmentPlatform && (
                <p className="text-sm text-red-600 mt-1">
                  {errors.manualAssessmentConfig.assessmentPlatform.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="assessment-link" className="flex items-center space-x-2">
                <ExternalLink className="h-4 w-4" />
                <span>Assessment Link</span>
              </Label>
              <Input
                id="assessment-link"
                type="url"
                {...register('manualAssessmentConfig.assessmentLink')}
                placeholder="https://platform.com/assessment/123"
              />
              {errors.manualAssessmentConfig?.assessmentLink && (
                <p className="text-sm text-red-600 mt-1">
                  {errors.manualAssessmentConfig.assessmentLink.message}
                </p>
              )}
            </div>
          </div>
        </div>

        <Separator />

        {/* Optional Settings */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
            <span>Optional Settings</span>
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="time-limit" className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Time Limit (minutes)</span>
                </Label>
                <Input
                  id="time-limit"
                  type="number"
                  min="15"
                  max="480"
                  {...register('manualAssessmentConfig.timeLimit', { valueAsNumber: true })}
                  placeholder="90"
                />
                <p className="text-xs text-gray-500 mt-1">15-480 minutes</p>
              </div>

              <div>
                <Label htmlFor="passing-score" className="flex items-center space-x-2">
                  <Target className="h-4 w-4" />
                  <span>Passing Score (%)</span>
                </Label>
                <Input
                  id="passing-score"
                  type="number"
                  min="0"
                  max="100"
                  {...register('manualAssessmentConfig.passingScore', { valueAsNumber: true })}
                  placeholder="75"
                />
                <p className="text-xs text-gray-500 mt-1">0-100 percentage</p>
              </div>

              <div>
                <Label htmlFor="max-attempts" className="flex items-center space-x-2">
                  <RotateCcw className="h-4 w-4" />
                  <span>Max Attempts</span>
                </Label>
                <Input
                  id="max-attempts"
                  type="number"
                  min="1"
                  max="5"
                  {...register('manualAssessmentConfig.maxAttempts', { valueAsNumber: true })}
                  placeholder="1"
                />
                <p className="text-xs text-gray-500 mt-1">1-5 attempts allowed</p>
              </div>
            </div>

            <div className="space-y-4">
              {/* Auto Grading Toggle */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-0.5">
                  <Label className="flex items-center space-x-2">
                    <Zap className="h-4 w-4" />
                    <span>Auto Grading</span>
                  </Label>
                  <p className="text-xs text-gray-500">
                    Assessment is automatically graded by the platform
                  </p>
                </div>
                <Switch
                  checked={watch('manualAssessmentConfig.autoGrading') || false}
                  onCheckedChange={(checked) => setValue('manualAssessmentConfig.autoGrading', checked)}
                />
              </div>

              {/* Instructions */}
              <div>
                <Label htmlFor="instructions" className="flex items-center space-x-2">
                  <BookOpen className="h-4 w-4" />
                  <span>Instructions for Candidate</span>
                </Label>
                <Textarea
                  id="instructions"
                  {...register('manualAssessmentConfig.instructions')}
                  placeholder="Provide detailed instructions for the candidate..."
                  rows={4}
                />
                <p className="text-xs text-gray-500 mt-1">
                  These instructions will be shown to the candidate before starting the assessment
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Assessment Preview */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h5 className="font-medium text-gray-900 mb-2">Assessment Preview</h5>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Platform:</span>
              <span className="font-medium">
                {watch('manualAssessmentConfig.assessmentPlatform') || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Time Limit:</span>
              <span className="font-medium">
                {watch('manualAssessmentConfig.timeLimit') 
                  ? `${watch('manualAssessmentConfig.timeLimit')} minutes`
                  : 'No limit'
                }
              </span>
            </div>
            <div className="flex justify-between">
              <span>Passing Score:</span>
              <span className="font-medium">
                {watch('manualAssessmentConfig.passingScore') 
                  ? `${watch('manualAssessmentConfig.passingScore')}%`
                  : 'Not specified'
                }
              </span>
            </div>
            <div className="flex justify-between">
              <span>Max Attempts:</span>
              <span className="font-medium">
                {watch('manualAssessmentConfig.maxAttempts') || 1}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Auto Grading:</span>
              <span className="font-medium">
                {watch('manualAssessmentConfig.autoGrading') ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </div>

        {/* Field Definitions Help */}
        {assessmentTypes?.assessmentTypes?.manual?.fieldDefinitions && Object.keys(assessmentTypes.assessmentTypes.manual.fieldDefinitions).length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h5 className="font-medium text-blue-900 mb-2">Field Definitions</h5>
            <div className="space-y-1 text-sm text-blue-800">
              {Object.entries(assessmentTypes.assessmentTypes.manual.fieldDefinitions).map(([field, definition]) => (
                <div key={field}>
                  <span className="font-medium capitalize">{field.replace(/([A-Z])/g, ' $1')}:</span>{' '}
                  {definition}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
