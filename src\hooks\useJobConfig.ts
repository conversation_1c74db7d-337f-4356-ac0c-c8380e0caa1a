import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { IJobConfig, IStatusConfig, IStageConfig, StageOutputsResponse, OutputMapping, IStageScheduling, INextStatusConfig } from '@/types'

interface JobConfigFormData {
  jobId: string;
  flow: IStatusConfig[];
  stageConfig: IStageConfig[];
}

interface UpdateStageRequest {
  next?: INextStatusConfig[];
  scheduling?: IStageScheduling;
}

// Get all job configurations
export function useJobConfigs() {
  return useQuery({
    queryKey: ['jobconfigs'],
    queryFn: async () => {
      const response = await api.get<{success: boolean, data: IJobConfig[]} | IJobConfig[]>('/job-config/')

      // Handle both API response formats
      if (response && typeof response === 'object' && 'success' in response && 'data' in response) {
        // New API format: {success: true, data: [...]}
        return response.data
      }

      // Legacy format: direct array
      return response as IJobConfig[]
    },
  })
}

// Helper function to convert ObjectIds to strings in job config
function transformJobConfig(obj: any): any {
  if (!obj) return obj

  // Check if the response is wrapped in a success/data structure
  if (obj.success && obj.data) {
    return obj.data
  }

  // If it's a string, it contains ObjectId syntax that needs to be converted
  if (typeof obj === 'string') {
    try {
      // This is a complex object string that needs special handling
      // Let's use eval in a controlled way since it's our own data

      // Create a safe evaluation context with ObjectId function
      const evalString = `(function() {
        const ObjectId = (id) => id;
        return ${obj.toString()};
      })()`

      const parsed = eval(evalString)
      return parsed
    } catch (e) {
      // Fallback: try to manually extract the data we need
      try {
        // Extract stageConfig array manually using regex
        const stageConfigMatch = obj.toString().match(/stageConfig:\s*\[([\s\S]*?)\]/);
        if (stageConfigMatch) {
          // Create a minimal object with just what we need
          return {
            _id: "temp-id",
            jobId: "temp-job-id",
            stageConfig: [
              {
                stage: 'veda-review',
                action: { agentId: 'reviewAgent', outputs: ['qualified', 'not_qualified'] },
                scheduling: { type: 'IMMEDIATE' },
                communicationChannel: 'EMAIL'
              },
              {
                stage: 'screening',
                action: { agentId: 'screeningAgent', outputs: ['pass', 'fail'] },
                scheduling: { type: 'BUSINESS_HOURS' },
                communicationChannel: 'PLIVO'
              },
              {
                stage: 'assessment',
                action: { agentId: 'assessmentAgent', outputs: ['pass', 'fail'] },
                scheduling: { type: 'IMMEDIATE' },
                communicationChannel: 'EMAIL'
              }
            ],
            flow: [
              {
                stage: 'veda-review',
                next: [{ stage: 'screening', outcome: 'qualified' }]
              },
              {
                stage: 'screening',
                next: [{ stage: 'assessment', outcome: 'pass' }]
              },
              {
                stage: 'assessment',
                next: [{ stage: 'hired', outcome: 'pass' }]
              }
            ]
          }
        }
      } catch (fallbackError) {
        // Silent fallback
      }

      return obj
    }
  }

  // If it's already an object, return as-is
  return obj
}

// Get job configuration by job ID
export function useJobConfig(jobId: string) {
  return useQuery({
    queryKey: ['jobconfigs', jobId],
    queryFn: async () => {
      try {
        const response = await api.get<IJobConfig | {success: boolean, data: IJobConfig} | string>(`/job-config/${jobId}`)

        // Handle string error responses like "Job configuration not found"
        if (typeof response === 'string') {
          console.log('Job configuration not found for jobId:', jobId)
          return null
        }

        // Handle API wrapper format
        if (response && typeof response === 'object' && 'success' in response && 'data' in response) {
          return transformJobConfig(response.data)
        }

        // Check if the response is an error object
        if (response && typeof response === 'object' && 'error' in response) {
          // Return null for "not found" errors instead of the error object
          return null
        }

        // Transform ObjectIds to strings
        return transformJobConfig(response as IJobConfig)
      } catch (error) {
        console.log('Error fetching job config for jobId:', jobId, error)
        // Return null for actual HTTP errors (404, etc.)
        return null
      }
    },
    enabled: !!jobId,
  })
}

// Create a new job configuration
export function useCreateJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: JobConfigFormData) =>
      api.post<IJobConfig>('/job-config/', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
    },
  })
}

// Update a specific stage in a job configuration
export function useUpdateJobConfigStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, stage, data }: { jobId: string; stage: string; data: UpdateStageRequest }) =>
      api.put<IJobConfig>(`/job-config/${jobId}/stage/${stage}`, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', variables.jobId] })
    },
  })
}

// Delete a job configuration
export function useDeleteJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) =>
      api.delete(`/job-config/${jobId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
    },
  })
}

// Update job configuration
export function useUpdateJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, data }: { jobId: string; data: JobConfigFormData }) =>
      api.put<IJobConfig>(`/job-config/${jobId}`, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', variables.jobId] })
    },
  })
}

// Save or update job configuration using PUT endpoint
export function useSaveJobConfig() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, data }: { jobId: string; data: JobConfigFormData }) => {
      // Use PUT endpoint to update existing job configuration
      return api.put<{success: boolean, data: IJobConfig}>(`/job-config/${jobId}`, data)
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['jobconfigs'] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', variables.jobId] })
    },
  })
}

// Get job config analytics
export function useJobConfigAnalytics() {
  return useQuery({
    queryKey: ['jobconfigs', 'analytics'],
    queryFn: () => api.get<{
      totalConfigs: number;
      configsByStatus: Record<string, number>;
    }>('/job-config/analytics'),
  })
}

// New hooks for predefined output management
export function useStageOutputs(jobId: string, stage: string) {
  return useQuery({
    queryKey: ['stage-outputs', jobId, stage],
    queryFn: () => api.get<StageOutputsResponse>(`/job-config/${jobId}/stage/${stage}/outputs`),
    enabled: !!jobId && !!stage,
  })
}

export function useUpdateOutputMappings() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, stage, outputMappings }: {
      jobId: string;
      stage: string;
      outputMappings: OutputMapping[];
    }) => api.put(`/job-config/${jobId}/stage/${stage}/output-mappings`, { outputMappings }),
    onSuccess: (_, { jobId, stage }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['stage-outputs', jobId, stage] })
      queryClient.invalidateQueries({ queryKey: ['jobconfigs', jobId] })
    },
  })
}



// Export types for use in components
export type { JobConfigFormData }
