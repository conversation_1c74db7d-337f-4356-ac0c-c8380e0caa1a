import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { ArrowLeft, Edit, Settings, Clock, MessageSquare, User, CheckCircle, AlertTriangle, Calendar, Briefcase } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useJobConfig } from '@/hooks/useJobConfig'
import { useJobs } from '@/hooks/useJobs'
import { formatDate } from '@/lib/utils'

export function WorkflowView() {
  const { jobId } = useParams<{ jobId: string }>()
  const navigate = useNavigate()
  
  const { data: jobConfig, isLoading: configLoading } = useJobConfig(jobId || '')
  const { data: jobs, isLoading: jobsLoading } = useJobs()

  const job = jobs?.find(j => j._id === jobId)

  if (configLoading || jobsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading workflow...</p>
        </div>
      </div>
    )
  }

  if (!jobConfig || !job) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Workflow Not Found</h3>
          <p className="text-gray-600 mb-4">The requested workflow configuration could not be found.</p>
          <Button onClick={() => navigate('/workflows')} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Workflows
          </Button>
        </div>
      </div>
    )
  }

  const getSchedulingDisplay = (scheduling: any) => {
    if (!scheduling || scheduling.type === 'IMMEDIATE') {
      return 'Immediate'
    }
    if (scheduling.type === 'BUSINESS_HOURS') {
      const params = scheduling.params || {}
      return `Business Hours (${params.startHour || 9}:00 - ${params.endHour || 17}:00)`
    }
    return scheduling.type
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'veda-review':
        return <User className="h-5 w-5" />
      case 'screening':
        return <MessageSquare className="h-5 w-5" />
      case 'assessment':
        return <CheckCircle className="h-5 w-5" />
      default:
        return <Settings className="h-5 w-5" />
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'veda-review':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'screening':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'assessment':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => navigate('/workflows')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Workflows
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{job.title} Workflow</h1>
            <div className="flex items-center space-x-4 mt-2 text-gray-600">
              <div className="flex items-center space-x-1">
                <Briefcase className="h-4 w-4" />
                <span>{job.department}</span>
              </div>
              <span>•</span>
              <span>{job.location}</span>
              <span>•</span>
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>Updated {formatDate(new Date(jobConfig.updatedAt || jobConfig.createdAt))}</span>
              </div>
            </div>
          </div>
        </div>
        <Button onClick={() => navigate(`/workflows/editor?jobId=${jobId}`)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Workflow
        </Button>
      </div>

      {/* Workflow Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Stages</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobConfig.stageConfig.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Flow Rules</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobConfig.flow.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Active</div>
          </CardContent>
        </Card>
      </div>

      {/* Stages Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Stage Configurations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {jobConfig.stageConfig.map((stage, index) => {
              const flowRule = jobConfig.flow.find(f => f.stage === stage.stage)
              
              return (
                <div key={stage.stage} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg border ${getStageColor(stage.stage)}`}>
                        {getStageIcon(stage.stage)}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold capitalize">
                          {stage.stage.replace('-', ' ')}
                        </h3>
                        <p className="text-sm text-gray-600">Stage {index + 1}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="capitalize">
                      {stage.stage}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Agent Configuration */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Agent</h4>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{stage.action.agentId}</span>
                      </div>
                    </div>

                    {/* Scheduling Configuration */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Scheduling</h4>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{getSchedulingDisplay(stage.scheduling)}</span>
                      </div>
                    </div>

                    {/* Communication Channels */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Communication</h4>
                      <div className="flex items-center space-x-2">
                        <MessageSquare className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          {stage.communicationChannels?.length || 0} channel(s)
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Flow Rules */}
                  {flowRule && flowRule.next && flowRule.next.length > 0 && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Outcomes & Next Steps</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {flowRule.next.map((nextStep, idx) => (
                            <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-2">
                                <Badge variant="secondary" className="text-xs">
                                  {nextStep.outcome}
                                </Badge>
                                <span className="text-sm text-gray-600">→</span>
                                <span className="text-sm font-medium">{nextStep.nextStage}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Custom Parameters */}
                  {stage.action.params && Object.keys(stage.action.params).length > 0 && (
                    <>
                      <Separator className="my-4" />
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Custom Parameters</h4>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                            {JSON.stringify(stage.action.params, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
