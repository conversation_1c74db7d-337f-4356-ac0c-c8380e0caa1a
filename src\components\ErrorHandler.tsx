import { useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { useToast } from '@/hooks/use-toast'
import { ApiError } from '@/lib/api'

export function ErrorHandler() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const { toast } = useToast()

  useEffect(() => {
    // Global error handler for React Query
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'error') {
        const error = event.error

        if (error instanceof ApiError) {
          // Handle authentication errors
          if (error.status === 401) {
            toast({
              title: "Authentication Error",
              description: error.message,
              variant: "destructive",
            })
            // Redirect to login page
            navigate('/login')
            return
          }

          // Handle other API errors
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          })
        } else {
          // Handle network or other errors
          toast({
            title: "Network Error",
            description: "Unable to connect to the server. Please check your internet connection.",
            variant: "destructive",
          })
        }
      }
    })

    // Global error handler for mutations
    const mutationUnsubscribe = queryClient.getMutationCache().subscribe((event) => {
      if (event.type === 'error') {
        const error = event.error

        if (error instanceof ApiError) {
          // Handle authentication errors
          if (error.status === 401) {
            toast({
              title: "Authentication Error",
              description: error.message,
              variant: "destructive",
            })
            navigate('/login')
            return
          }

          // Handle validation errors
          if (error.status === 400) {
            toast({
              title: "Validation Error",
              description: error.message,
              variant: "destructive",
            })
            return
          }

          // Handle other API errors
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          })
        }
      }
    })

    return () => {
      unsubscribe()
      mutationUnsubscribe()
    }
  }, [queryClient, navigate, toast])

  return null // This component doesn't render anything
}
