import { useState } from 'react'
import { Eye, Edit, Trash2, Mail, Phone, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DataTable } from '@/components/tables/DataTable'
import { CandidateStageManager } from './CandidateStageManager'
import { CandidateDetail } from '@/pages/candidates/CandidateDetail'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useJobCandidates, type ICandidate } from '@/hooks/useCandidates'
import { formatDate } from '@/lib/utils'

interface JobCandidatesListProps {
  jobId: string
  jobTitle?: string
  onEditCandidate?: (candidate: ICandidate) => void
  onDeleteCandidate?: (candidateId: string) => void
}

export function JobCandidatesList({ 
  jobId, 
  jobTitle, 
  onEditCandidate, 
  onDeleteCandidate 
}: JobCandidatesListProps) {
  const [viewingCandidate, setViewingCandidate] = useState<ICandidate | null>(null)
  const { data: candidates, isLoading, error } = useJobCandidates(jobId)

  const handleViewCandidate = (candidate: ICandidate) => {
    setViewingCandidate(candidate)
  }

  const handleEditCandidate = (candidate: ICandidate) => {
    onEditCandidate?.(candidate)
  }

  const handleDeleteCandidate = (candidateId: string) => {
    if (confirm('Are you sure you want to delete this candidate?')) {
      onDeleteCandidate?.(candidateId)
    }
  }

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      'registered': 'bg-blue-100 text-blue-800',
      'pending_schedule': 'bg-yellow-100 text-yellow-800',
      'queued': 'bg-purple-100 text-purple-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'awaiting_result': 'bg-indigo-100 text-indigo-800',
      'completed_success': 'bg-green-100 text-green-800',
      'completed_fail': 'bg-red-100 text-red-800',
      'workflow_terminated': 'bg-gray-100 text-gray-800',
      'error': 'bg-red-100 text-red-800',
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  const columns = [
    {
      key: 'name',
      label: 'Candidate',
      sortable: true,
      render: (value: string, row: ICandidate) => (
        <div className="min-w-[200px]">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-medium text-gray-900 truncate">{value}</div>
              <div className="flex items-center space-x-4 mt-1">
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Mail className="h-3 w-3 flex-shrink-0" />
                  <span>{row.email}</span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Phone className="h-3 w-3 flex-shrink-0" />
                  <span>{row.phone}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'stage',
      label: 'Current Stage',
      sortable: true,
      render: (value: string, row: ICandidate) => (
        <div className="min-w-[160px]">
          <CandidateStageManager candidate={row} compact />
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => (
        <div className="min-w-[120px]">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(value)}`}>
            {value.replace('_', ' ').toUpperCase()}
          </span>
        </div>
      )
    },
    {
      key: 'source',
      label: 'Source',
      sortable: true,
      render: (value: string) => (
        <div className="min-w-[120px]">
          <span className="text-sm text-gray-900">{value}</span>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Applied Date',
      sortable: true,
      render: (value: string | Date) => (
        <div className="min-w-[120px]">
          <span className="text-sm text-gray-900">{formatDate(value)}</span>
        </div>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: ICandidate) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewCandidate(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          {onEditCandidate && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditCandidate(row)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDeleteCandidate && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteCandidate(row._id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      )
    }
  ]

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            Error loading candidates: {error.message}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>
            Candidates {jobTitle && `for ${jobTitle}`}
            {candidates && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({candidates.length} total)
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={candidates || []}
            columns={columns}
            isLoading={isLoading}
            emptyMessage="No candidates found for this job"
          />
        </CardContent>
      </Card>

      {/* Candidate Detail Dialog */}
      <Dialog open={!!viewingCandidate} onOpenChange={() => setViewingCandidate(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Candidate Details</DialogTitle>
          </DialogHeader>
          {viewingCandidate && (
            <CandidateDetail
              candidate={viewingCandidate}
              onBack={() => setViewingCandidate(null)}
              onEdit={onEditCandidate || (() => {})}
              onDelete={onDeleteCandidate || (() => {})}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
