import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ArrowR<PERSON> } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useJobs } from '@/hooks/useJobs'
import type { IJobConfig } from '@/types'
import { formatDate } from '@/lib/utils'

interface JobConfigDetailProps {
  jobConfig: IJobConfig
  onBack: () => void
  onEdit: (config: IJobConfig) => void
  onDelete: (id: string) => void
}

export function JobConfigDetail({ jobConfig, onBack, onEdit, onDelete }: JobConfigDetailProps) {
  const { data: jobs } = useJobs()

  const getJobTitle = (jobId: string) => {
    const job = jobs?.find(j => j._id === jobId)
    return job?.title || 'Unknown Job'
  }

  const getJobDetails = (jobId: string) => {
    const job = jobs?.find(j => j._id === jobId)
    return job
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this job configuration?')) {
      await onDelete(jobConfig._id)
      onBack()
    }
  }

  const job = getJobDetails(jobConfig.jobId)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {getJobTitle(jobConfig.jobId)} Configuration
            </h1>
            <p className="text-gray-600">
              Workflow configuration details
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => onEdit(jobConfig)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Job Information */}
      {job && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Job Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Title</p>
                <p className="text-sm">{job.title}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Department</p>
                <p className="text-sm">{job.department}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Location</p>
                <p className="text-sm">{job.location}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Job Type</p>
                <Badge variant="secondary">{job.jobType}</Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Experience Level</p>
                <Badge variant="outline">{job.experienceLevel}</Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <Badge variant={job.status === 'active' ? 'default' : 'secondary'}>
                  {job.status}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Workflow Flow */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Workflow className="h-5 w-5 mr-2" />
            Workflow Flow ({jobConfig.flow?.length || 0} stages)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {jobConfig.flow?.map((flowStage, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium flex items-center">
                    Stage {index + 1}: 
                    <Badge variant="outline" className="ml-2">
                      {flowStage.stage}
                    </Badge>
                  </h4>
                  <Badge variant="secondary">
                    {flowStage.next?.length || 0} outcomes
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">Possible Outcomes:</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {flowStage.next?.map((nextStage, nextIndex) => (
                      <div key={nextIndex} className="flex items-center space-x-2 bg-gray-50 p-2 rounded">
                        <Badge variant="outline" className="text-xs">
                          {nextStage.outcome}
                        </Badge>
                        <ArrowRight className="h-3 w-3 text-gray-400" />
                        <Badge variant="secondary" className="text-xs">
                          {nextStage.stage}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Stage Configurations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Stage Configurations ({jobConfig.stageConfig?.length || 0} configs)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {jobConfig.stageConfig?.map((stageConfig, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium flex items-center">
                    <Badge variant="outline" className="mr-2">
                      {stageConfig.stage}
                    </Badge>
                    Configuration
                  </h4>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Agent</p>
                    <p className="text-sm">{stageConfig.action?.agentId || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Scheduling</p>
                    <Badge variant="secondary" className="text-xs">
                      {stageConfig.scheduling?.type || 'IMMEDIATE'}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Communication</p>
                    <Badge variant="outline" className="text-xs">
                      {stageConfig.communicationChannel || 'EMAIL'}
                    </Badge>
                  </div>
                </div>

                {stageConfig.action?.outputs && stageConfig.action.outputs.length > 0 && (
                  <div className="mt-3">
                    <p className="text-sm font-medium text-gray-500 mb-2">Expected Outputs:</p>
                    <div className="flex flex-wrap gap-1">
                      {stageConfig.action.outputs.map((output, outputIndex) => (
                        <Badge key={outputIndex} variant="secondary" className="text-xs">
                          {output}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {stageConfig.scheduling?.type === 'BUSINESS_HOURS' && stageConfig.scheduling.params && (
                  <div className="mt-3 p-3 bg-gray-50 rounded">
                    <p className="text-sm font-medium text-gray-600 mb-2">Business Hours Settings:</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                      {stageConfig.scheduling.params.timezone && (
                        <div>
                          <span className="font-medium">Timezone:</span> {stageConfig.scheduling.params.timezone}
                        </div>
                      )}
                      {stageConfig.scheduling.params.startHour !== undefined && (
                        <div>
                          <span className="font-medium">Start:</span> {stageConfig.scheduling.params.startHour}:00
                        </div>
                      )}
                      {stageConfig.scheduling.params.endHour !== undefined && (
                        <div>
                          <span className="font-medium">End:</span> {stageConfig.scheduling.params.endHour}:00
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Created</p>
              <p className="text-sm">{formatDate(jobConfig.createdAt)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Last Updated</p>
              <p className="text-sm">{formatDate(jobConfig.updatedAt)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
