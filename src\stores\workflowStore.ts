import { create } from 'zustand'
import {
  addEdge,
  applyNode<PERSON>hang<PERSON>,
  applyEdgeChanges,
  type Node,
  type Edge,
  type <PERSON>deChang<PERSON>,
  type EdgeChange,
  type Connection
} from 'reactflow'
import type { IJobConfig, IStatusConfig, IStageConfig, ICommunicationChannel, IStageScheduling, IStageAction, StageOutputsResponse, OutputMapping } from '@/types'

// Extended node data for workflow stages
export interface WorkflowStageData {
  stage: string
  label: string
  agentId?: string
  outputs?: string[]               // Static outputs from stage mapping
  predefinedOutputs?: string[]     // READ-ONLY: Outputs from agent definition
  outputMappings?: OutputMapping[] // USER-CONFIGURABLE: Output to stage mappings
  params?: Record<string, any>
  scheduling?: IStageScheduling
  communicationChannel?: ICommunicationChannel
  isConfigured?: boolean
}

// Extended node type for workflow
export interface WorkflowNode extends Node {
  data: WorkflowStageData
}

// Workflow store state
interface WorkflowState {
  // Canvas state
  nodes: WorkflowNode[]
  edges: Edge[]
  selectedNode: WorkflowNode | null

  // Configuration state
  currentJobConfig: IJobConfig | null
  isConfigPanelOpen: boolean

  // New state for output mapping
  stageOutputsCache: Map<string, StageOutputsResponse> // Cache stage outputs
  pendingMappingChanges: Map<string, OutputMapping[]> // Unsaved changes
  
  // Canvas actions
  setNodes: (nodes: WorkflowNode[]) => void
  setEdges: (edges: Edge[]) => void
  onNodesChange: (changes: NodeChange[]) => void
  onEdgesChange: (changes: EdgeChange[]) => void
  onConnect: (connection: Connection) => void
  
  // Node selection and configuration
  selectNode: (node: WorkflowNode | null) => void
  updateNodeData: (nodeId: string, data: Partial<WorkflowStageData>) => void
  
  // Configuration panel
  openConfigPanel: () => void
  closeConfigPanel: () => void
  
  // Workflow management
  loadWorkflow: (jobConfig: IJobConfig) => void
  saveWorkflow: () => IJobConfig | null
  createNewWorkflow: (jobId: string) => void

  // Enhanced actions for output mapping
  updateOutputMappings: (stage: string, mappings: OutputMapping[]) => void
  loadStageOutputs: (stage: string, outputs: StageOutputsResponse) => void
  generateEdgesFromMappings: () => void // Regenerate visual edges from mappings
  addStageNode: (stage: string, position: { x: number; y: number }) => void
  removeNode: (nodeId: string) => void

  // Computed properties
  selectedStageId: string | null

  // Utility functions
  getNodeById: (id: string) => WorkflowNode | undefined
  validateWorkflow: () => { isValid: boolean; errors: string[] }

  // Flow management
  updateStageFlow: (stageId: string, mappings: { outcome: string; targetStage: string }[]) => void
  regenerateAllEdgesFromFlow: () => void
}

// Static stage-agent-output mappings
const stageAgentMappings = {
  'veda-review': {
    agentId: 'reviewAgent',
    outputs: ['best', 'good', 'bad'],
    defaultParams: {
      reviewCriteria: 'Match candidate profile against job description keywords and basic qualifications.'
    }
  },
  'screening': {
    agentId: 'screeningAgent',
    outputs: ['pass', 'fail'],
    defaultParams: {
      screeningConfig: {
        questions: [
          {
            id: 1,
            question: 'Are you comfortable with remote work?',
            type: 'yes-no',
            options: ['Yes', 'No'],
            correctAnswer: 'Yes',
            required: true
          },
          {
            id: 2,
            question: 'Do you have experience with the required technologies?',
            type: 'yes-no',
            options: ['Yes', 'No'],
            correctAnswer: 'Yes',
            required: true
          }
        ],
        passingScore: 80,
        timeLimit: 15,
        allowRetries: false,
        maxRetries: 0
      }
    }
  },
  'assessment': {
    agentId: 'assessmentAgent',
    outputs: ['pass', 'fail'],
    defaultParams: {
      assessmentType: 'technical',
      duration: 60
    }
  },
  'interview': {
    agentId: 'interviewAgent',
    outputs: ['pass', 'fail'],
    defaultParams: {
      interviewType: 'technical',
      duration: 45
    }
  },
  'final-review': {
    agentId: 'reviewAgent',
    outputs: ['approved', 'rejected'],
    defaultParams: {
      finalCriteria: 'Overall candidate evaluation'
    }
  }
} as const

// Default stage configurations
const defaultStages = [
  { id: 'veda-review', label: 'Veda Review', color: '#8B5CF6' },
  { id: 'screening', label: 'Screening', color: '#F59E0B' },
  { id: 'assessment', label: 'Assessment', color: '#EF4444' },
]

// Create the workflow store
export const useWorkflowStore = create<WorkflowState>((set, get) => ({
  // Initial state
  nodes: [],
  edges: [],
  selectedNode: null,
  currentJobConfig: null,
  isConfigPanelOpen: false,
  stageOutputsCache: new Map(),
  pendingMappingChanges: new Map(),

  // Computed properties
  get selectedStageId() {
    return get().selectedNode?.id || null
  },

  // Canvas actions
  setNodes: (nodes) => set({ nodes }),
  setEdges: (edges) => set({ edges }),
  
  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes)
    })
  },
  
  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges)
    })
  },
  
  onConnect: (connection) => {
    const { edges } = get()
    const newEdge = {
      ...connection,
      id: `${connection.source}-${connection.target}`,
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#6B7280', strokeWidth: 2 },
    }
    set({
      edges: addEdge(newEdge, edges)
    })
  },

  // Node selection and configuration
  selectNode: (node) => {
    set({ 
      selectedNode: node,
      isConfigPanelOpen: !!node
    })
  },
  
  updateNodeData: (nodeId, newData) => {
    const { nodes } = get()
    const updatedNodes = nodes.map(node => 
      node.id === nodeId 
        ? { 
            ...node, 
            data: { 
              ...node.data, 
              ...newData,
              isConfigured: true
            }
          }
        : node
    )
    set({ nodes: updatedNodes })
  },

  // Configuration panel
  openConfigPanel: () => set({ isConfigPanelOpen: true }),
  closeConfigPanel: () => set({ isConfigPanelOpen: false, selectedNode: null }),

  // Workflow management
  loadWorkflow: (jobConfig) => {
    const nodes: WorkflowNode[] = []
    const edges: Edge[] = []

    // Validate jobConfig structure
    if (!jobConfig) {
      console.warn('No jobConfig provided')
      set({
        currentJobConfig: null,
        nodes: [],
        edges: [],
        selectedNode: null,
        isConfigPanelOpen: false
      })
      return
    }

    if (!jobConfig.stageConfig || !Array.isArray(jobConfig.stageConfig)) {
      set({
        currentJobConfig: jobConfig,
        nodes: [],
        edges: [],
        selectedNode: null,
        isConfigPanelOpen: false
      })
      return
    }

    console.log('loadWorkflow: Loading job config with flow:', jobConfig.flow?.map(f =>
      `${f.stage} -> [${f.next?.map(n => `${n.outcome}:${n.stage}`).join(', ') || 'none'}]`
    ))

    // First, collect all unique stage names from the flow configuration
    const stageNamesFromFlow = new Set<string>()
    if (jobConfig.flow && Array.isArray(jobConfig.flow)) {
      jobConfig.flow.forEach(flowConfig => {
        stageNamesFromFlow.add(flowConfig.stage)
        if (flowConfig.next) {
          flowConfig.next.forEach(nextConfig => {
            if (nextConfig.stage !== 'stop' && nextConfig.stage !== 'rejected') {
              stageNamesFromFlow.add(nextConfig.stage)
            }
          })
        }
      })
    }

    // Create a mapping from stage name to stage config for easy lookup
    const stageConfigMap = new Map<string, typeof jobConfig.stageConfig[0]>()
    jobConfig.stageConfig.forEach(config => {
      stageConfigMap.set(config.stage, config)
    })

    // Create nodes using stage names from flow, but apply stage configurations by matching stage names
    const seenStageNames = new Set<string>()
    let nodeIndex = 0

    stageNamesFromFlow.forEach(stageName => {
      if (seenStageNames.has(stageName)) return
      seenStageNames.add(stageName)

      const stageConfig = stageConfigMap.get(stageName)

      if (stageConfig) {
        const stageInfo = defaultStages.find(s => s.id === stageName) ||
                         { id: stageName, label: stageName, color: '#6B7280' }

        // Create node with unique ID but clean stage name
        const nodeId = `${stageName}_${Date.now()}_${nodeIndex}`

        nodes.push({
          id: nodeId, // Use unique node ID for UI
          type: 'workflowStage',
          position: { x: 200 + (nodeIndex % 3) * 300, y: 100 + Math.floor(nodeIndex / 3) * 200 },
          data: {
            stage: stageName, // Store the clean stage name for logic
            label: stageInfo.label,
            agentId: stageConfig.action?.agentId,
            outputs: stageConfig.action?.outputs,
            params: stageConfig.action?.params || {},
            scheduling: stageConfig.scheduling,
            communicationChannel: stageConfig.communicationChannel,
            isConfigured: true,
          },
          style: {
            backgroundColor: stageInfo.color,
            color: 'white',
            border: 'none',
            borderRadius: '8px',
          }
        })
        nodeIndex++
      } else {
        console.warn('No stage config found for stage:', stageName)
      }
    })

    // If no flow configuration exists, create nodes from stageConfig only
    if (stageNamesFromFlow.size === 0) {
      jobConfig.stageConfig.forEach((stageConfig, index) => {
        const stageInfo = defaultStages.find(s => s.id === stageConfig.stage) ||
                         { id: stageConfig.stage, label: stageConfig.stage, color: '#6B7280' }

        nodes.push({
          id: stageConfig.stage,
          type: 'workflowStage',
          position: { x: 200 + (index % 3) * 300, y: 100 + Math.floor(index / 3) * 200 },
          data: {
            stage: stageConfig.stage,
            label: stageInfo.label,
            agentId: stageConfig.action?.agentId,
            outputs: stageConfig.action?.outputs,
            params: stageConfig.action?.params || {},
            scheduling: stageConfig.scheduling,
            communicationChannel: stageConfig.communicationChannel,
            isConfigured: true,
          },
          style: {
            backgroundColor: stageInfo.color,
            color: 'white',
            border: 'none',
            borderRadius: '8px',
          }
        })
      })
    }

    console.log('loadWorkflow: Created nodes:', nodes.map(n => `${n.id} (${n.data.stage})`))

    set({
      currentJobConfig: jobConfig,
      nodes,
      edges: [], // Start with empty edges, will be regenerated
      selectedNode: null,
      isConfigPanelOpen: false
    })

    // Regenerate edges from flow configuration to ensure visual consistency
    get().regenerateAllEdgesFromFlow()
  },

  saveWorkflow: () => {
    const { nodes, currentJobConfig } = get()

    if (!currentJobConfig) return null

    // Build stageConfig from nodes
    const stageConfig: IStageConfig[] = nodes.map(node => ({
      stage: node.data.stage,
      scheduling: node.data.scheduling,
      action: {
        agentId: node.data.agentId || 'defaultAgent',
        outputs: node.data.outputs || ['success', 'fail'],
        params: node.data.params || {},
      },
      communicationChannel: node.data.communicationChannel,
    }))

    // Use the existing flow configuration from currentJobConfig instead of rebuilding from edges
    // This preserves the properly configured outcome-to-stage mappings set via updateStageFlow
    const flow = currentJobConfig.flow || []

    console.log('saveWorkflow: Using existing flow configuration:', flow.map(f =>
      `${f.stage} -> [${f.next?.map(n => `${n.outcome}:${n.stage}`).join(', ') || 'none'}]`
    ))

    const updatedConfig = {
      ...currentJobConfig,
      flow,
      stageConfig,
      updatedAt: new Date(),
    }

    // Update the current job config in the store
    set({ currentJobConfig: updatedConfig })

    return updatedConfig
  },

  createNewWorkflow: (jobId) => {
    const newJobConfig: IJobConfig = {
      _id: `config_${Date.now()}`,
      jobId,
      flow: [],
      stageConfig: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    set({
      currentJobConfig: newJobConfig,
      nodes: [],
      edges: [],
      selectedNode: null,
      isConfigPanelOpen: false,
      stageOutputsCache: new Map(),
      pendingMappingChanges: new Map(),
    })
  },

  // Enhanced methods for output mapping
  updateOutputMappings: (stage: string, mappings: OutputMapping[]) => {
    const { pendingMappingChanges } = get()
    const newChanges = new Map(pendingMappingChanges)
    newChanges.set(stage, mappings)

    set({ pendingMappingChanges: newChanges })

    // Regenerate visual edges
    get().generateEdgesFromMappings()
  },

  loadStageOutputs: (stage: string, outputs: StageOutputsResponse) => {
    const { stageOutputsCache } = get()
    const newCache = new Map(stageOutputsCache)
    newCache.set(stage, outputs)

    set({ stageOutputsCache: newCache })
  },

  generateEdgesFromMappings: () => {
    const { nodes, stageOutputsCache, pendingMappingChanges } = get()
    const newEdges: Edge[] = []

    // Utility function for consistent output coloring
    const getOutputColor = (output: string): string => {
      const colors: Record<string, string> = {
        'best': '#22c55e',    // green
        'good': '#3b82f6',    // blue
        'bad': '#ef4444',     // red
        'pass': '#22c55e',    // green
        'fail': '#ef4444',    // red
        'qualified': '#22c55e',
        'not-qualified': '#ef4444',
      }
      return colors[output] || '#6b7280' // gray fallback
    }

    nodes.forEach(node => {
      if (node.type === 'stage') {
        const stageData = node.data as WorkflowStageData
        const stage = stageData.stage
        const mappings = pendingMappingChanges.get(stage) ||
                        stageOutputsCache.get(stage)?.currentMappings || []

        mappings.forEach((mapping) => {
          const targetNode = nodes.find(n =>
            n.type === 'stage' && (n.data as WorkflowStageData).stage === mapping.targetStage
          )

          if (targetNode) {
            newEdges.push({
              id: `${node.id}-${mapping.output}-${targetNode.id}`,
              source: node.id,
              target: targetNode.id,
              sourceHandle: mapping.output,
              label: mapping.output,
              data: {
                output: mapping.output,
                outcome: mapping.output, // Legacy compatibility
                isConfigurable: true
              },
              style: {
                stroke: getOutputColor(mapping.output),
                strokeWidth: 2,
              }
            })
          }
        })
      }
    })

    set({ edges: newEdges })
  },

  addStageNode: (stage, position) => {
    const { nodes } = get()
    const stageInfo = defaultStages.find(s => s.id === stage) ||
                     { id: stage, label: stage, color: '#6B7280' }
    const mapping = stageAgentMappings[stage as keyof typeof stageAgentMappings]

    const newNode: WorkflowNode = {
      id: `${stage}_${Date.now()}`,
      type: 'workflowStage',
      position,
      data: {
        stage,
        label: stageInfo.label,
        agentId: mapping?.agentId,
        outputs: mapping?.outputs ? [...mapping.outputs] : undefined,
        params: mapping?.defaultParams,
        isConfigured: false,
      },
      style: {
        backgroundColor: stageInfo.color,
        color: 'white',
        border: 'none',
        borderRadius: '8px',
      }
    }

    set({ nodes: [...nodes, newNode] })
  },

  removeNode: (nodeId) => {
    const { nodes, edges, selectedNode } = get()

    // Remove the node
    const updatedNodes = nodes.filter(node => node.id !== nodeId)

    // Remove all edges connected to this node
    const updatedEdges = edges.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    )

    // Clear selection if the removed node was selected
    const updatedSelectedNode = selectedNode?.id === nodeId ? null : selectedNode

    set({
      nodes: updatedNodes,
      edges: updatedEdges,
      selectedNode: updatedSelectedNode,
      isConfigPanelOpen: updatedSelectedNode ? get().isConfigPanelOpen : false
    })
  },

  // Utility functions
  getNodeById: (id) => {
    return get().nodes.find(node => node.id === id)
  },

  validateWorkflow: () => {
    const { nodes, edges } = get()
    const errors: string[] = []

    // Check if workflow has nodes
    if (nodes.length === 0) {
      errors.push('Workflow must have at least one stage')
    }

    // Check if all nodes are configured
    const unconfiguredNodes = nodes.filter(node => !node.data.isConfigured)
    if (unconfiguredNodes.length > 0) {
      errors.push(`${unconfiguredNodes.length} stage(s) need configuration`)
    }

    // Only validate connections for multi-stage workflows
    // Single-stage workflows are valid without connections
    if (nodes.length > 1) {
      // Check for disconnected nodes (except start/end nodes)
      nodes.forEach(node => {
        const hasIncoming = edges.some(edge => edge.target === node.id)
        const hasOutgoing = edges.some(edge => edge.source === node.id)

        if (!hasIncoming && !hasOutgoing && node.data.stage !== 'start') {
          errors.push(`Stage "${node.data.label}" is not connected`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  // Update flow mappings for a specific stage
  updateStageFlow: (stageId: string, mappings: { outcome: string; targetStage: string }[]) => {
    console.log('updateStageFlow called for stage:', stageId, 'with mappings:', mappings.map(m => `${m.outcome} → ${m.targetStage}`))

    const { currentJobConfig, nodes } = get()
    if (!currentJobConfig) return

    // Extract clean stage name from node ID (e.g., "veda-review_1753952536798" -> "veda-review")
    const sourceNode = nodes.find(n => n.id === stageId)
    const cleanStageName = sourceNode ? sourceNode.data.stage : (stageId.includes('_') ? stageId.split('_')[0] : stageId)

    // Update the flow configuration using clean stage names
    const updatedFlow = currentJobConfig.flow.filter(f => f.stage !== cleanStageName)

    if (mappings.length > 0) {
      // Convert target stage IDs to clean stage names
      const cleanMappings = mappings.map(m => {
        const targetNode = nodes.find(n => n.id === m.targetStage)
        const cleanTargetStage = targetNode ? targetNode.data.stage : (m.targetStage.includes('_') ? m.targetStage.split('_')[0] : m.targetStage)
        return {
          stage: cleanTargetStage,
          outcome: m.outcome
        }
      })

      updatedFlow.push({
        stage: cleanStageName,
        next: cleanMappings
      })
    }

    // Update the job config
    const updatedJobConfig = {
      ...currentJobConfig,
      flow: updatedFlow,
      updatedAt: new Date()
    }

    // Regenerate ALL edges based on complete flow configuration
    const newEdges: Edge[] = []

    // Use the complete updated flow to regenerate all edges
    updatedFlow.forEach((flowConfig) => {
      if (flowConfig.next && Array.isArray(flowConfig.next)) {
        flowConfig.next.forEach((nextConfig) => {
          // Find nodes by stage name (since flow now uses clean stage names)
          let sourceNode = nodes.find(n => n.data.stage === flowConfig.stage)
          let targetNode = nodes.find(n => n.data.stage === nextConfig.stage)

          // Only create edges to actual nodes (not terminal stages)
          if (sourceNode && targetNode && nextConfig.stage !== 'stop' && nextConfig.stage !== 'rejected') {
            newEdges.push({
              id: `${sourceNode.id}-${targetNode.id}-${nextConfig.outcome}`,
              source: sourceNode.id,
              target: targetNode.id,
              type: 'smoothstep',
              animated: true,
              label: nextConfig.outcome,
              style: { stroke: '#6B7280', strokeWidth: 2 },
              labelStyle: { fontSize: '12px', fontWeight: 'bold' },
            })
          }
        })
      }
    })

    console.log('updateStageFlow: Regenerated edges:', newEdges.map(e => `${e.source} -> ${e.target} (${e.label})`))

    set({
      currentJobConfig: updatedJobConfig,
      edges: newEdges
    })
  },

  // Regenerate all edges from current flow configuration
  regenerateAllEdgesFromFlow: () => {
    const { nodes, currentJobConfig } = get()

    if (!currentJobConfig?.flow) {
      set({ edges: [] })
      return
    }

    const newEdges: Edge[] = []

    currentJobConfig.flow.forEach((flowConfig) => {
      if (flowConfig.next && Array.isArray(flowConfig.next)) {
        flowConfig.next.forEach((nextConfig) => {
          // Find nodes by stage name (since flow now uses clean stage names)
          let sourceNode = nodes.find(n => n.data.stage === flowConfig.stage)
          let targetNode = nodes.find(n => n.data.stage === nextConfig.stage)

          // Only create edges to actual nodes (not terminal stages)
          if (sourceNode && targetNode && nextConfig.stage !== 'stop' && nextConfig.stage !== 'rejected') {
            newEdges.push({
              id: `${sourceNode.id}-${targetNode.id}-${nextConfig.outcome}`,
              source: sourceNode.id,
              target: targetNode.id,
              type: 'smoothstep',
              animated: true,
              label: nextConfig.outcome,
              style: { stroke: '#6B7280', strokeWidth: 2 },
              labelStyle: { fontSize: '12px', fontWeight: 'bold' },
            })
          } else {
            console.log('regenerateAllEdgesFromFlow: Could not find nodes for edge:', {
              sourceStage: flowConfig.stage,
              targetStage: nextConfig.stage,
              outcome: nextConfig.outcome,
              sourceNode: sourceNode?.id,
              targetNode: targetNode?.id,
              availableNodes: nodes.map(n => `${n.id} (${n.data.stage})`)
            })
          }
        })
      }
    })

    console.log('regenerateAllEdgesFromFlow: Generated edges:', newEdges.map(e => `${e.source} -> ${e.target} (${e.label})`))
    set({ edges: newEdges })
  },

  // Generate JobConfig object from current workflow
  generateJobConfig: (jobId: string): IJobConfig => {
    const { nodes, currentJobConfig } = get()

    // Use existing flow configuration if available, otherwise generate from nodes
    let flow: IStatusConfig[] = []

    if (currentJobConfig?.flow && currentJobConfig.flow.length > 0) {
      // Use the properly configured flow from currentJobConfig
      flow = currentJobConfig.flow
      console.log('generateJobConfig: Using existing flow configuration:', flow.map(f =>
        `${f.stage} -> [${f.next?.map(n => `${n.outcome}:${n.stage}`).join(', ') || 'none'}]`
      ))
    } else {
      // Fallback: generate basic flow structure for nodes without proper configuration
      console.log('generateJobConfig: No existing flow configuration, generating basic structure')
      nodes.forEach(node => {
        const stage = node.data.stage
        const mapping = stageAgentMappings[stage as keyof typeof stageAgentMappings]

        if (mapping) {
          // Create basic flow with terminal states for unconfigured workflows
          const next = mapping.outputs.map(output => {
            if (['best', 'good', 'pass', 'approved'].includes(output)) {
              return { stage: 'stop', outcome: output }
            } else {
              return { stage: 'rejected', outcome: output }
            }
          })

          flow.push({ stage, next })
        }
      })
    }

    // Generate stageConfig from nodes using clean stage names
    const stageConfig: IStageConfig[] = nodes.map(node => {
      const stage = node.data.stage
      const mapping = stageAgentMappings[stage as keyof typeof stageAgentMappings]

      return {
        stage,
        action: {
          agentId: mapping?.agentId || node.data.agentId || 'defaultAgent',
          outputs: mapping?.outputs ? [...mapping.outputs] : (node.data.outputs || ['success', 'fail']),
          params: node.data.params || mapping?.defaultParams || {}
        },
        communicationChannel: node.data.communicationChannel || 'EMAIL',
        scheduling: node.data.scheduling || { type: 'IMMEDIATE', params: {} }
      }
    })

    return {
      _id: `config_${jobId}_${Date.now()}`,
      jobId,
      flow,
      stageConfig,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
}))

// Export default stages and mappings for use in components
export { defaultStages, stageAgentMappings }
