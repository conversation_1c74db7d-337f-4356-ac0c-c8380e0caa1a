import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  ReactFlowProvider,
  Panel,
  type Connection,
  type Edge,
} from 'reactflow'
import { CheckCircle, AlertTriangle } from 'lucide-react'
import 'reactflow/dist/style.css'

import { WorkflowStageNode } from './WorkflowStageNode'
import { StageConfigPanel } from './StageConfigPanel'
import { WorkflowToolbar } from './WorkflowToolbar'
import { useWorkflowStore } from '@/stores/workflowStore'

// Define custom node types outside component to prevent React Flow warnings
const nodeTypes = {
  workflowStage: WorkflowStageNode,
}

// Custom edge styles
const defaultEdgeOptions = {
  animated: true,
  style: {
    strokeWidth: 2,
    stroke: '#6B7280',
  },
}

interface WorkflowCanvasProps {
  jobId?: string
  readonly?: boolean
}

function WorkflowCanvasInner({ jobId, readonly = false }: WorkflowCanvasProps) {
  const [showOverview, setShowOverview] = useState(false)

  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    selectNode,
    isConfigPanelOpen,
    validateWorkflow,
  } = useWorkflowStore()

  // Handle node click for selection
  const handleNodeClick = useCallback((event: React.MouseEvent, node: any) => {
    if (!readonly) {
      selectNode(node)
    }
  }, [selectNode, readonly])

  // Handle pane click to deselect
  const handlePaneClick = useCallback(() => {
    if (!readonly) {
      selectNode(null)
    }
  }, [selectNode, readonly])

  // Handle connection creation
  const handleConnect = useCallback((params: Connection | Edge) => {
    if (!readonly) {
      onConnect(params)
    }
  }, [onConnect, readonly])

  // Memoize the ReactFlow props to prevent unnecessary re-renders
  const reactFlowProps = useMemo(() => ({
    nodes,
    edges,
    onNodesChange: readonly ? undefined : onNodesChange,
    onEdgesChange: readonly ? undefined : onEdgesChange,
    onConnect: handleConnect,
    onNodeClick: handleNodeClick,
    onPaneClick: handlePaneClick,
    nodeTypes,
    defaultEdgeOptions,
    fitView: false,
    defaultZoom: 0.8,
    defaultViewport: { x: 0, y: 50, zoom: 1.1 },
    minZoom: 0.1,
    maxZoom: 2,
    attributionPosition: 'bottom-left' as const,
    proOptions: { hideAttribution: true },
  }), [
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    handleConnect,
    handleNodeClick,
    handlePaneClick,
    readonly
  ])

  return (
    <div className="w-full h-full relative">
      <ReactFlow {...reactFlowProps}>
        {/* Background with dots pattern */}
        <Background
          color="#e5e7eb"
          gap={20}
          size={1}
        />
        
        {/* Controls for zoom, fit view, etc. */}
        <Controls
          position="bottom-right"
          showInteractive={!readonly}
          style={{ bottom: '120px', right: '20px' }}
        />

        {/* Mini map for navigation */}
        <MiniMap
          position="bottom-left"
          nodeColor={(node) => {
            switch (node.data?.stage) {
              case 'veda-review': return '#8B5CF6'
              case 'screening': return '#F59E0B'
              case 'assessment': return '#EF4444'
              default: return '#6B7280'
            }
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
          style={{ bottom: '120px', left: '20px' }}
          pannable={true}
          zoomable={true}
        />

        {/* Workflow Overview Badge */}
        <Panel position="top-right">
          <div className="flex flex-col items-end space-y-2">
            {/* Circular Badge */}
            <button
              onClick={() => setShowOverview(!showOverview)}
              className={`w-8 h-8 rounded-full shadow-md border-2 flex items-center justify-center transition-all duration-200 hover:scale-105 ${
                validateWorkflow().isValid
                  ? 'bg-white border-green-500 text-green-600 hover:bg-green-50'
                  : 'bg-red-500 border-red-600 text-white hover:bg-red-600'
              }`}
            >
              {validateWorkflow().isValid ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertTriangle className="h-4 w-4" />
              )}
            </button>

            {/* Overview Panel - shown when badge is clicked */}
            {showOverview && (
              <div className="bg-white rounded-md shadow-md p-3 max-w-xs border border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-semibold text-gray-900">Overview</h3>
                  <div className="flex items-center space-x-1">
                    {validateWorkflow().isValid ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        <span className="text-[10px] font-medium">Valid</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-red-600">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        <span className="text-[10px] font-medium">Issues</span>
                      </div>
                    )}
                  </div>
                </div>

            <div className="space-y-2">
              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-blue-50 p-2 rounded-md">
                  <div className="text-sm font-bold text-blue-600">{nodes.length}</div>
                  <div className="text-[10px] text-blue-600 font-medium">Stages</div>
                </div>
                <div className="bg-green-50 p-2 rounded-md">
                  <div className="text-sm font-bold text-green-600">{edges.length}</div>
                  <div className="text-[10px] text-green-600 font-medium">Connections</div>
                </div>
                <div className="bg-purple-50 p-2 rounded-md">
                  <div className="text-sm font-bold text-purple-600">
                    {nodes.filter(n => n.data.isConfigured).length}
                  </div>
                  <div className="text-[10px] text-purple-600 font-medium">Configured</div>
                </div>
                <div className="bg-orange-50 p-2 rounded-md">
                  <div className="text-sm font-bold text-orange-600">
                    {validateWorkflow().errors.length}
                  </div>
                  <div className="text-[10px] text-orange-600 font-medium">Issues</div>
                </div>
              </div>

              {/* Progress Bar */}
              {nodes.length > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>Configuration Progress</span>
                    <span>{Math.round((nodes.filter(n => n.data.isConfigured).length / nodes.length) * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${(nodes.filter(n => n.data.isConfigured).length / nodes.length) * 100}%`
                      }}
                    />
                  </div>
                </div>
              )}

              {/* Validation Errors */}
              {!validateWorkflow().isValid && (
                <div className="space-y-1">
                  <div className="text-xs font-medium text-red-700">Issues to Fix:</div>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {validateWorkflow().errors.slice(0, 3).map((error, index) => (
                      <div key={`error-${index}`} className="text-xs text-red-600 bg-red-50 p-2 rounded border-l-2 border-red-200">
                        {error}
                      </div>
                    ))}
                    {validateWorkflow().errors.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{validateWorkflow().errors.length - 3} more issues
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
              </div>
            )}
          </div>
        </Panel>
      </ReactFlow>

      {/* Configuration Panel */}
      {!readonly && <StageConfigPanel />}

      {/* Floating Toolbar - positioned inside the canvas */}
      {!readonly && <WorkflowToolbar jobId={jobId} />}
    </div>
  )
}

export function WorkflowCanvas(props: WorkflowCanvasProps) {
  return (
    <ReactFlowProvider>
      <WorkflowCanvasInner {...props} />
    </ReactFlowProvider>
  )
}
