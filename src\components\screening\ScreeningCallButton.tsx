import { useState } from 'react'
import { Phone } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useTriggerScreeningCall } from '@/hooks/useScreeningCall'
import type { IJobConfig } from '@/types'

interface ScreeningCallButtonProps {
  jobConfig: IJobConfig | null
  candidatePhone?: string
  candidateName?: string
  disabled?: boolean
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
}

/**
 * Button component for triggering screening calls
 * Uses the job configuration to get screening questions
 */
export function ScreeningCallButton({
  jobConfig,
  candidatePhone = '',
  candidateName = 'Candidate',
  disabled = false,
  variant = 'default',
  size = 'default'
}: ScreeningCallButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState(candidatePhone)
  const { triggerCall, isLoading } = useTriggerScreeningCall()

  const handleTriggerCall = async () => {
    if (!phoneNumber.trim()) {
      return
    }

    try {
      await triggerCall(jobConfig, phoneNumber)
      setIsOpen(false)
    } catch (error) {
      // Error is handled by the hook's onError callback
      console.error('Failed to trigger screening call:', error)
    }
  }

  // Check if screening questions are available
  const hasScreeningQuestions = jobConfig?.stageConfig?.some(
    stage => stage.stage === 'screening' && 
    stage.action?.params?.screeningConfig?.questions?.length > 0
  )

  if (!hasScreeningQuestions) {
    return (
      <Button disabled variant="outline" size={size}>
        <Phone className="h-4 w-4 mr-2" />
        No Screening Questions
      </Button>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button disabled={disabled} variant={variant} size={size}>
          <Phone className="h-4 w-4 mr-2" />
          Start Screening Call
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Initiate Screening Call</DialogTitle>
          <DialogDescription>
            Start an automated screening call for {candidateName}. The system will call the candidate and ask the configured screening questions.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              placeholder="+1234567890"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </div>
          <div className="text-sm text-gray-600">
            <p>Questions configured: {
              jobConfig?.stageConfig
                ?.find(stage => stage.stage === 'screening')
                ?.action?.params?.screeningConfig?.questions?.length || 0
            }</p>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleTriggerCall} 
            disabled={!phoneNumber.trim() || isLoading}
          >
            {isLoading ? 'Initiating...' : 'Start Call'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

/**
 * Simple button for quick screening calls when phone number is already known
 */
export function QuickScreeningCallButton({
  jobConfig,
  candidatePhone,
  candidateName = 'Candidate',
  disabled = false
}: Omit<ScreeningCallButtonProps, 'variant' | 'size'>) {
  const { triggerCall, isLoading } = useTriggerScreeningCall()

  const handleQuickCall = async () => {
    if (!candidatePhone?.trim()) {
      return
    }

    try {
      await triggerCall(jobConfig, candidatePhone)
    } catch (error) {
      console.error('Failed to trigger screening call:', error)
    }
  }

  const hasScreeningQuestions = jobConfig?.stageConfig?.some(
    stage => stage.stage === 'screening' && 
    stage.action?.params?.screeningConfig?.questions?.length > 0
  )

  if (!hasScreeningQuestions || !candidatePhone) {
    return null
  }

  return (
    <Button 
      onClick={handleQuickCall} 
      disabled={disabled || isLoading}
      variant="outline"
      size="sm"
    >
      <Phone className="h-4 w-4 mr-2" />
      {isLoading ? 'Calling...' : 'Screen Call'}
    </Button>
  )
}
