import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { ArrowRight, Plus, Trash2 } from 'lucide-react'
import { useWorkflowStore } from '@/stores/workflowStore'

interface FlowConfigPanelProps {
  stageId: string
  outputs: string[]
  currentMappings?: { outcome: string; targetStage: string }[]
  onMappingsChange: (mappings: { outcome: string; targetStage: string }[]) => void
  onReset?: () => void
}

export function FlowConfigPanel({
  stageId,
  outputs,
  currentMappings = [],
  onMappingsChange,
  onReset
}: FlowConfigPanelProps) {
  const { nodes } = useWorkflowStore()
  
  // Get available target stages (all stages except current one)
  const availableStages = nodes
    .filter(node => node.id !== stageId)
    .map(node => ({
      id: node.id,
      label: node.data.label,
      stage: node.data.stage
    }))
  
  // Add terminal stages
  const terminalStages = [
    { id: 'stop', label: 'Stop (Success)', stage: 'stop' },
    { id: 'rejected', label: 'Rejected', stage: 'rejected' }
  ]
  
  const allTargetStages = [...availableStages, ...terminalStages]

  // Debug: Log available target stages
  React.useEffect(() => {
    console.log('Available target stages for', stageId, ':', allTargetStages.map(s => `${s.id} (${s.label})`))
  }, [stageId, allTargetStages.length])

  // Get default target stage based on outcome
  const getDefaultTargetStage = (outcome: string): string => {
    if (['best', 'good', 'pass', 'approved'].includes(outcome.toLowerCase())) {
      return 'stop'
    }
    return 'rejected'
  }

  // Reset to default mappings
  const resetToDefaults = () => {
    const defaultMappings = outputs.map(output => ({
      outcome: output,
      targetStage: getDefaultTargetStage(output)
    }))
    console.log('Resetting to default mappings:', defaultMappings.map(m => `${m.outcome} → ${m.targetStage}`))
    onMappingsChange(defaultMappings)
    onReset?.() // Notify parent that user has reset mappings
  }

  // Initialize mappings if empty
  React.useEffect(() => {
    if (currentMappings.length === 0 && outputs.length > 0) {
      const defaultMappings = outputs.map(output => ({
        outcome: output,
        targetStage: getDefaultTargetStage(output)
      }))
      console.log('Initializing default mappings:', defaultMappings.map(m => `${m.outcome} → ${m.targetStage}`))
      onMappingsChange(defaultMappings)
    }
  }, [outputs, currentMappings.length, onMappingsChange])
  
  // Handle mapping change
  const handleMappingChange = (outcome: string, targetStage: string) => {
    // Don't allow empty target stages
    if (!targetStage) {
      console.warn('Empty target stage for outcome:', outcome)
      return
    }

    const updatedMappings = currentMappings.map(mapping =>
      mapping.outcome === outcome
        ? { ...mapping, targetStage }
        : mapping
    )

    // If mapping doesn't exist, add it
    if (!currentMappings.find(m => m.outcome === outcome)) {
      updatedMappings.push({ outcome, targetStage })
    }

    console.log('Updated mappings after change:', updatedMappings.map(m => `${m.outcome} → ${m.targetStage}`))
    onMappingsChange(updatedMappings)
  }
  
  // Get current target stage for an outcome
  const getCurrentTargetStage = (outcome: string): string => {
    const mapping = currentMappings.find(m => m.outcome === outcome)
    return mapping?.targetStage || getDefaultTargetStage(outcome)
  }
  
  // Get stage label by id
  const getStageLabel = (stageId: string): string => {
    const stage = allTargetStages.find(s => s.id === stageId)
    return stage?.label || stageId
  }

  console.log('FlowConfigPanel rendering for stage:', stageId, 'with outputs:', outputs)

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ArrowRight className="h-4 w-4" />
            Flow Configuration
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={resetToDefaults}
            className="h-6 px-2 text-xs"
          >
            Reset to Defaults
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-xs text-muted-foreground mb-3">
          Configure where candidates go for each outcome from this stage.
        </div>
        
        {outputs.map((outcome) => (
          <div key={outcome} className="space-y-2">
            <Label className="text-xs font-medium">
              When outcome is "{outcome}"
            </Label>
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <Select
                  value={getCurrentTargetStage(outcome)}
                  onValueChange={(value) => handleMappingChange(outcome, value)}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select target stage" />
                  </SelectTrigger>
                  <SelectContent>
                    {allTargetStages.map((stage) => (
                      <SelectItem key={stage.id} value={stage.id} className="text-xs">
                        {stage.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="text-xs text-muted-foreground min-w-0 flex-shrink-0">
                → {getStageLabel(getCurrentTargetStage(outcome))}
              </div>
            </div>
          </div>
        ))}
        
        {outputs.length === 0 && (
          <div className="text-xs text-muted-foreground text-center py-4">
            No outcomes defined for this stage
          </div>
        )}
        
        <div className="pt-2 border-t">
          <div className="text-xs text-muted-foreground">
            <strong>Current Flow:</strong>
            {currentMappings.map((mapping, index) => (
              <div key={mapping.outcome} className="mt-1">
                {mapping.outcome} → {getStageLabel(mapping.targetStage)}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
