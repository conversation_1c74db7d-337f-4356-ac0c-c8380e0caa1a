import { useState, useEffect, useCallback } from 'react'
import { SearchFilter } from './SearchFilter'
import type { <PERSON>Field, FilterField } from '@/types/search'
import { useSearchCandidates, useFilterCandidates, type ICandidate } from '@/hooks/useCandidates'
import { useJobs } from '@/hooks/useJobs'

interface CandidateSearchFilterProps {
  onResultsChange: (candidates: ICandidate[] | null) => void
  className?: string
}

export function CandidateSearchFilter({ onResultsChange, className }: CandidateSearchFilterProps) {
  const [searchParams, setSearchParams] = useState<{ name?: string; email?: string }>({})
  const [filterParams, setFilterParams] = useState<{ stage?: string; status?: string; jobId?: string }>({})
  
  const { data: jobs } = useJobs()
  
  // Search and filter queries
  const { data: searchResults } = useSearchCandidates(searchParams)
  const { data: filterResults } = useFilterCandidates(filterParams)

  // Combine search and filter results
  useEffect(() => {
    const hasSearch = Object.values(searchParams).some(value => value)
    const hasFilter = Object.values(filterParams).some(value => value)

    if (hasSearch && hasFilter) {
      // If both search and filter are active, we need to intersect results
      // For now, prioritize search results and apply filters client-side
      if (searchResults && filterResults) {
        const searchIds = new Set(searchResults.map(c => c._id))
        const intersected = filterResults.filter(c => searchIds.has(c._id))
        onResultsChange(intersected)
      } else {
        onResultsChange(searchResults || filterResults || null)
      }
    } else if (hasSearch) {
      onResultsChange(searchResults || null)
    } else if (hasFilter) {
      onResultsChange(filterResults || null)
    } else {
      onResultsChange(null) // Show all candidates
    }
  }, [searchResults, filterResults, searchParams, filterParams])

  const searchFields: SearchField[] = [
    {
      key: 'name',
      label: 'Name',
      placeholder: 'Search by candidate name...',
      type: 'text'
    },
    {
      key: 'email',
      label: 'Email',
      placeholder: 'Search by email address...',
      type: 'text'
    }
  ]

  const filterFields: FilterField[] = [
    {
      key: 'stage',
      label: 'Stage',
      type: 'select',
      options: [
        { value: 'applied', label: 'Applied' },
        { value: 'screening', label: 'Screening' },
        { value: 'interview', label: 'Interview' },
        { value: 'technical', label: 'Technical' },
        { value: 'final', label: 'Final Round' },
        { value: 'offer', label: 'Offer' },
        { value: 'hired', label: 'Hired' },
        { value: 'rejected', label: 'Rejected' }
      ]
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'hired', label: 'Hired' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'withdrawn', label: 'Withdrawn' }
      ]
    },
    {
      key: 'jobId',
      label: 'Job',
      type: 'select',
      options: jobs?.map(job => ({
        value: job._id,
        label: job.title
      })) || []
    }
  ]

  const handleSearchChange = useCallback((params: Record<string, string>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '')
    )
    setSearchParams(filteredParams)
  }, [])

  const handleFilterChange = useCallback((params: Record<string, string | string[]>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) =>
        Array.isArray(value) ? value.length > 0 : value !== ''
      )
    )
    setFilterParams(filteredParams)
  }, [])

  const handleClear = useCallback(() => {
    setSearchParams({})
    setFilterParams({})
  }, [])

  return (
    <SearchFilter
      searchFields={searchFields}
      filterFields={filterFields}
      onSearchChange={handleSearchChange}
      onFilterChange={handleFilterChange}
      onClear={handleClear}
      className={className}
    />
  )
}
