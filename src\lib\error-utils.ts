import { ApiError } from './api'

export interface ValidationError {
  field: string
  message: string
}

export function extractValidationErrors(error: unknown): ValidationError[] {
  if (!(error instanceof ApiError)) {
    return []
  }

  // Handle different validation error formats from the API
  const data = error.data

  // Format 1: { errors: { field: "message" } }
  if (data?.errors && typeof data.errors === 'object') {
    return Object.entries(data.errors).map(([field, message]) => ({
      field,
      message: String(message),
    }))
  }

  // Format 2: { details: [{ field: "field", message: "message" }] }
  if (data?.details && Array.isArray(data.details)) {
    return data.details.map((detail: any) => ({
      field: detail.field || detail.path || 'unknown',
      message: detail.message || String(detail),
    }))
  }

  // Format 3: { field: "message" } (direct field errors)
  if (data && typeof data === 'object' && !data.message && !data.error) {
    return Object.entries(data).map(([field, message]) => ({
      field,
      message: String(message),
    }))
  }

  return []
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof ApiError) {
    return error.message
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'An unexpected error occurred'
}

export function isNetworkError(error: unknown): boolean {
  return error instanceof TypeError && error.message.includes('fetch')
}

export function isAuthenticationError(error: unknown): boolean {
  return error instanceof ApiError && error.status === 401
}

export function isValidationError(error: unknown): boolean {
  return error instanceof ApiError && error.status === 400
}

export function isForbiddenError(error: unknown): boolean {
  return error instanceof ApiError && error.status === 403
}

export function isNotFoundError(error: unknown): boolean {
  return error instanceof ApiError && error.status === 404
}

export function isServerError(error: unknown): boolean {
  return error instanceof ApiError && error.status >= 500
}
