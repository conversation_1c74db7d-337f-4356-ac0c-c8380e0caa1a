import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import type { 
  IAssessmentConfig, 
  AssessmentTypesResponse, 
  CreateAssessmentRequest, 
  UpdateAssessmentRequest,
  AssessmentType 
} from '@/types'

// Get assessment types and field definitions
export function useAssessmentTypes() {
  return useQuery({
    queryKey: ['assessment-types'],
    queryFn: () => api.get<AssessmentTypesResponse>('/assessment/types'),
  })
}

// Get all assessment configurations with optional filters
export function useAssessments(filters?: {
  jobId?: string
  stage?: string
  assessmentType?: AssessmentType
}) {
  return useQuery({
    queryKey: ['assessments', filters],
    queryFn: () => {
      const params = new URLSearchParams()
      if (filters?.jobId) params.append('jobId', filters.jobId)
      if (filters?.stage) params.append('stage', filters.stage)
      if (filters?.assessmentType) params.append('assessmentType', filters.assessmentType)
      
      const queryString = params.toString()
      return api.get<{ assessmentConfigs: IAssessmentConfig[], total: number }>(
        `/assessment${queryString ? `?${queryString}` : ''}`
      )
    },
  })
}

// Get assessment configuration by ID
export function useAssessment(configId: string) {
  return useQuery({
    queryKey: ['assessment', configId],
    queryFn: () => api.get<{ assessmentConfig: IAssessmentConfig }>(`/assessment/${configId}`),
    enabled: !!configId,
  })
}

// Get assessment configuration by job and stage
export function useAssessmentByJobStage(jobId: string, stage: string) {
  return useQuery({
    queryKey: ['assessment', 'job', jobId, 'stage', stage],
    queryFn: () => api.get<{ assessmentConfig: IAssessmentConfig }>(`/assessment/job/${jobId}/stage/${stage}`),
    enabled: !!jobId && !!stage,
  })
}

// Create assessment configuration
export function useCreateAssessment() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (data: CreateAssessmentRequest) =>
      api.post<{ message: string; assessmentConfig: IAssessmentConfig }>('/assessment', data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      queryClient.invalidateQueries({ 
        queryKey: ['assessment', 'job', variables.jobId, 'stage', variables.stage] 
      })
      toast({
        title: 'Assessment Created',
        description: response.message,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Create Assessment',
        description: error.response?.data?.error || 'An unexpected error occurred',
        variant: 'destructive',
      })
    },
  })
}

// Update assessment configuration
export function useUpdateAssessment() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ configId, data }: { configId: string; data: UpdateAssessmentRequest }) =>
      api.put<{ message: string; assessmentConfig: IAssessmentConfig }>(`/assessment/${configId}`, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      queryClient.invalidateQueries({ queryKey: ['assessment', variables.configId] })
      if (variables.data.jobId && variables.data.stage) {
        queryClient.invalidateQueries({ 
          queryKey: ['assessment', 'job', variables.data.jobId, 'stage', variables.data.stage] 
        })
      }
      toast({
        title: 'Assessment Updated',
        description: response.message,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Update Assessment',
        description: error.response?.data?.error || 'An unexpected error occurred',
        variant: 'destructive',
      })
    },
  })
}

// Delete assessment configuration
export function useDeleteAssessment() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (configId: string) =>
      api.delete<{ message: string; deletedConfig: any }>(`/assessment/${configId}`),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      toast({
        title: 'Assessment Deleted',
        description: response.message,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Delete Assessment',
        description: error.response?.data?.error || 'An unexpected error occurred',
        variant: 'destructive',
      })
    },
  })
}

// Toggle assessment configuration status
export function useToggleAssessmentStatus() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: (configId: string) =>
      api.patch<{ message: string; assessmentConfig: any }>(`/assessment/${configId}/toggle`),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['assessments'] })
      toast({
        title: 'Assessment Status Updated',
        description: response.message,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to Update Status',
        description: error.response?.data?.error || 'An unexpected error occurred',
        variant: 'destructive',
      })
    },
  })
}
