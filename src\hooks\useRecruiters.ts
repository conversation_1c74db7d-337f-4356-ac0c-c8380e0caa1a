import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'

// Temporary type definitions until import issues are fixed
interface IRecruiter {
  _id: string;
  name: string;
  email: string;
  organization: string;
  password: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface RecruiterFormData {
  name: string;
  email: string;
  organization: string;
  password: string;
}

export function useRecruiters() {
  return useQuery({
    queryKey: ['recruiters'],
    queryFn: () => api.get<IRecruiter[]>('/recruiter'),
  })
}

export function useCreateRecruiter() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: RecruiterFormData) =>
      api.post<IRecruiter>('/recruiter', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recruiters'] })
    },
  })
}

export function useUpdateRecruiter() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: RecruiterFormData }) =>
      api.put<IRecruiter>(`/recruiter/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recruiters'] })
    },
  })
}

export function useDeleteRecruiter() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => api.delete(`/recruiter/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recruiters'] })
    },
  })
}

// Get recruiter's jobs
export function useRecruiterJobs() {
  return useQuery({
    queryKey: ['recruiters', 'jobs'],
    queryFn: () => api.get<Array<{
      _id: string;
      title: string;
      department: string;
      status: string;
      createdBy: string;
      createdAt: string;
    }>>('/recruiter/jobs'),
  })
}

// Get recruiter's candidates
export function useRecruiterCandidates() {
  return useQuery({
    queryKey: ['recruiters', 'candidates'],
    queryFn: () => api.get<Array<{
      _id: string;
      name: string;
      email: string;
      jobId: string;
      stage: string;
      status: string;
      createdAt: string;
    }>>('/recruiter/candidates'),
  })
}

// Get specific job by ID (authenticated)
export function useRecruiterJob(jobId: string) {
  return useQuery({
    queryKey: ['recruiters', 'jobs', jobId],
    queryFn: () => api.get(`/recruiter/jobs/${jobId}`),
    enabled: !!jobId,
  })
}

// Create a new job
export function useCreateRecruiterJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) => api.post('/recruiter/jobs', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs'] })
    },
  })
}

// Update a job
export function useUpdateRecruiterJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, data }: { jobId: string; data: any }) =>
      api.put(`/recruiter/jobs/${jobId}`, data),
    onSuccess: (_, { jobId }) => {
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs'] })
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs', jobId] })
    },
  })
}

// Delete a job
export function useDeleteRecruiterJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => api.delete(`/recruiter/jobs/${jobId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs'] })
    },
  })
}

// Get candidates for a specific job
export function useRecruiterJobCandidates(jobId: string) {
  return useQuery({
    queryKey: ['recruiters', 'jobs', jobId, 'candidates'],
    queryFn: () => api.get(`/recruiter/jobs/${jobId}/candidates`),
    enabled: !!jobId,
  })
}

// Shortlist a candidate for a specific job
export function useShortlistCandidate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, candidateId }: { jobId: string; candidateId: string }) =>
      api.post(`/recruiter/jobs/${jobId}/candidates/${candidateId}/shortlist`),
    onSuccess: (_, { jobId }) => {
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs', jobId, 'candidates'] })
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Reject a candidate for a specific job
export function useRejectCandidate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ jobId, candidateId }: { jobId: string; candidateId: string }) =>
      api.post(`/recruiter/jobs/${jobId}/candidates/${candidateId}/reject`),
    onSuccess: (_, { jobId }) => {
      queryClient.invalidateQueries({ queryKey: ['recruiters', 'jobs', jobId, 'candidates'] })
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Get recruiter analytics
export function useRecruiterAnalytics() {
  return useQuery({
    queryKey: ['recruiters', 'analytics'],
    queryFn: () => api.get<{
      totalRecruiters: number;
      recruitersByOrganization: Record<string, number>;
    }>('/recruiter/analytics'),
  })
}