import { useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'

interface StartWorkflowResponse {
  success: boolean
  message: string
  data?: any
}

/**
 * Hook for starting/triggering a job workflow
 * Calls POST /workflows/:jobId/start endpoint
 */
export function useStartWorkflow() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (jobId: string): Promise<StartWorkflowResponse> => {
      // Make API call to start workflow
      return api.post<StartWorkflowResponse>(`/job/workflows/${jobId}/start`)
    },
    onSuccess: (data, jobId) => {
      if (data.success) {
        toast({
          title: "Workflow Started",
          description: data.message || "The workflow has been successfully started.",
        })
        
        // Invalidate relevant queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['workflows'] })
        queryClient.invalidateQueries({ queryKey: ['jobConfigs'] })
        queryClient.invalidateQueries({ queryKey: ['candidates'] })
        queryClient.invalidateQueries({ queryKey: ['workflow', jobId] })
      } else {
        toast({
          title: "Failed to Start Workflow",
          description: data.message || "Failed to start the workflow.",
          variant: "destructive",
        })
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "An error occurred while starting the workflow.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook for starting multiple workflows
 */
export function useStartMultipleWorkflows() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (jobIds: string[]): Promise<{ results: Array<{ jobId: string; success: boolean; message?: string }> }> => {
      const results = await Promise.allSettled(
        jobIds.map(async (jobId) => {
          try {
            const response = await api.post<StartWorkflowResponse>(`/workflows/${jobId}/start`)
            return { jobId, success: response.success, message: response.message }
          } catch (error) {
            return { 
              jobId, 
              success: false, 
              message: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        })
      )

      return {
        results: results.map((result, index) => {
          if (result.status === 'fulfilled') {
            return result.value
          } else {
            return {
              jobId: jobIds[index],
              success: false,
              message: result.reason?.message || 'Failed to start workflow'
            }
          }
        })
      }
    },
    onSuccess: (data) => {
      const successful = data.results.filter(r => r.success).length
      const failed = data.results.filter(r => !r.success).length

      if (successful > 0) {
        toast({
          title: "Workflows Started",
          description: `${successful} workflow(s) started successfully${failed > 0 ? `, ${failed} failed` : ''}.`,
        })
      }

      if (failed > 0 && successful === 0) {
        toast({
          title: "Failed to Start Workflows",
          description: `All ${failed} workflow(s) failed to start.`,
          variant: "destructive",
        })
      }
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['workflows'] })
      queryClient.invalidateQueries({ queryKey: ['jobConfigs'] })
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "An error occurred while starting workflows.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook with confirmation dialog for starting workflow
 */
export function useStartWorkflowWithConfirmation() {
  const startWorkflowMutation = useStartWorkflow()

  const startWorkflow = async (jobId: string, jobTitle?: string) => {
    // You can add confirmation logic here if needed
    // For now, directly start the workflow
    return startWorkflowMutation.mutateAsync(jobId)
  }

  return {
    startWorkflow,
    isLoading: startWorkflowMutation.isPending,
    error: startWorkflowMutation.error
  }
}

// Export types for use in components
export type { StartWorkflowResponse }
