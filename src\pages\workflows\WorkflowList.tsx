import { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Plus, Play, Settings, Briefcase, Calendar, CheckCircle, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/tables/DataTable'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useJobs } from '@/hooks/useJobs'
import { useJobConfigs, useDeleteJobConfig } from '@/hooks/useJobConfig'
import { useStartWorkflow } from '@/hooks/useStartWorkflow'
import { useAuth } from '@/contexts/AuthContext'
import { formatDate } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

export function WorkflowList() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const { user } = useAuth()
  const { data: jobs, isLoading: jobsLoading } = useJobs()
  const { data: jobConfigs, isLoading: configsLoading } = useJobConfigs()
  const deleteJobConfigMutation = useDeleteJobConfig()
  const startWorkflowMutation = useStartWorkflow()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedJobId, setSelectedJobId] = useState<string>('')

  // Get available jobs for workflow creation (jobs without existing workflows)
  const availableJobs = useMemo(() => {
    if (!jobs || !jobConfigs || !user) return []

    // Filter jobs by user's organization
    const organizationJobs = jobs.filter(job => {
      const jobOrgId = typeof job.organization === 'string' ? job.organization : job.organization._id
      const userOrgId = typeof user.organization === 'string' ? user.organization : user.organization._id
      return jobOrgId === userOrgId
    })

    // Filter out jobs that already have workflows
    const jobsWithConfigs = new Set(jobConfigs?.map(config => config.jobId) || [])
    return organizationJobs.filter(job => !jobsWithConfigs.has(job._id))
  }, [jobs, user, jobConfigs])

  // Filter jobs by user's organization and create workflow data
  const workflows = useMemo(() => {
    if (!jobs || !user || !jobConfigs || jobConfigs.length === 0) return []

    // Filter jobs by user's organization
    const organizationJobs = jobs.filter(job => {
      const jobOrgId = typeof job.organization === 'string' ? job.organization : job.organization._id
      const userOrgId = typeof user.organization === 'string' ? user.organization : user.organization._id
      return jobOrgId === userOrgId
    })

    // Only show jobs that have configurations - filter out jobs without configs
    const result = organizationJobs
      .map(job => {
        const jobConfig = jobConfigs.find(config => config.jobId === job._id)

        if (jobConfig) {
          // Job has a configuration - show it
          return {
            _id: jobConfig._id,
            jobId: job._id,
            name: `${job.title} Workflow`,
            status: 'active' as const,
            stagesCount: jobConfig.stageConfig.length,
            connectionsCount: jobConfig.flow.length,
            configuredStages: jobConfig.stageConfig.length,
            lastModified: new Date(jobConfig.updatedAt || jobConfig.createdAt),
            createdAt: new Date(jobConfig.createdAt),
            isValid: jobConfig.stageConfig.length > 0 && jobConfig.flow.length > 0,
            hasConfig: true,
            job: job
          }
        }

        // Job doesn't have a configuration - don't show it (return null)
        return null
      })
      .filter(workflow => workflow !== null) // Remove null entries

    return result
  }, [jobs, jobConfigs, user])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleEdit = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      navigate(`/workflows/editor?jobId=${workflow.jobId}`)
    }
  }

  const handleView = (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow) {
      if (workflow.hasConfig) {
        // Navigate to detailed workflow view
        navigate(`/workflows/view/${workflow.jobId}`)
      } else {
        // Navigate to editor to create workflow
        navigate(`/workflows/editor?jobId=${workflow.jobId}`)
      }
    }
  }

  const handleDelete = async (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)

    if (!workflow) {
      toast({
        title: "Error",
        description: "Workflow not found.",
        variant: "destructive",
      })
      return
    }

    if (!workflow.hasConfig) {
      toast({
        title: "No Configuration",
        description: "This workflow has no configuration to delete.",
        variant: "destructive",
      })
      return
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete the workflow configuration for "${workflow.job.title}"?\n\nThis action cannot be undone.`
    )

    if (!confirmed) {
      return
    }

    try {
      await deleteJobConfigMutation.mutateAsync(workflow.jobId)
      toast({
        title: "Workflow Deleted",
        description: `Workflow configuration for "${workflow.job.title}" has been deleted successfully.`,
      })
    } catch (error: any) {
      console.error('Delete workflow error:', error)
      toast({
        title: "Delete Failed",
        description: error?.message || "Failed to delete workflow configuration. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleCreateNew = () => {
    if (availableJobs.length === 0) {
      toast({
        title: "No Available Jobs",
        description: "All jobs already have workflows configured. Create a new job first.",
        variant: "destructive",
      })
      return
    }

    if (availableJobs.length === 1) {
      // If only one job available, directly navigate to editor
      navigate(`/workflows/editor?jobId=${availableJobs[0]._id}`)
    } else {
      // Multiple jobs available, show selection dialog
      setIsCreateDialogOpen(true)
    }
  }

  const handleJobSelection = () => {
    if (selectedJobId) {
      navigate(`/workflows/editor?jobId=${selectedJobId}`)
      setIsCreateDialogOpen(false)
      setSelectedJobId('')
    }
  }

  const handleStartWorkflow = async (workflowId: string) => {
    const workflow = workflows.find(w => w._id === workflowId)
    if (workflow && workflow.hasConfig) {
      try {
        await startWorkflowMutation.mutateAsync(workflow.jobId)
        toast({
          title: "Workflow Started",
          description: `Workflow for ${workflow.job.title} has been started.`,
        })
      } catch (error) {
        toast({
          title: "Start Failed",
          description: "Failed to start workflow.",
          variant: "destructive",
        })
      }
    }
  }

  const columns = [
    {
      key: 'name',
      label: 'Workflow',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[250px]">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                row.hasConfig
                  ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                  : 'bg-gradient-to-br from-gray-400 to-gray-500'
              }`}>
                <Settings className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-semibold text-gray-900 truncate">{value}</div>
              <div className="text-xs text-gray-500 flex items-center space-x-1 mt-1">
                <Briefcase className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{row.job.title}</span>
              </div>
              <div className="text-xs text-gray-500 mt-0.5">
                {row.job.department} • {row.job.location}
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string, row: any) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-2">
            <Badge className={`${getStatusColor(value)} border-0 text-xs px-2 py-1`}>
              {value.toUpperCase()}
            </Badge>
            {row.isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
            )}
          </div>
        </div>
      )
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      sortable: true,
      render: (value: Date) => (
        <div className="min-w-[120px]">
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="text-sm text-gray-600">{formatDate(value)}</span>
          </div>
        </div>
      )
    },
  ]

  // Calculate stats
  const totalWorkflows = workflows.length
  const activeWorkflows = workflows.filter(w => w.status === 'active').length
  const configuredWorkflows = workflows.filter(w => w.hasConfig).length
  const validWorkflows = workflows.filter(w => w.isValid).length

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Total Workflows</p>
              <p className="text-xl font-bold">{totalWorkflows}</p>
            </div>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Active</p>
              <p className="text-xl font-bold text-green-600">{activeWorkflows}</p>
            </div>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Configured</p>
              <p className="text-xl font-bold text-blue-600">{configuredWorkflows}</p>
            </div>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Valid</p>
              <p className="text-xl font-bold text-blue-600">{validWorkflows}</p>
            </div>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardContent>
        </Card>
        <Card
          className="border-2 border-dashed border-gray-300 hover:border-primary hover:bg-gray-50 transition-colors cursor-pointer"
          onClick={handleCreateNew}
        >
          <CardContent className="flex items-center justify-center px-3 py-0 h-full">
            <div className="text-center">
              <Plus className="h-6 w-6 text-gray-400 mx-auto mb-1" />
              <p className="text-sm font-medium text-gray-600">Create Workflow</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflows Table */}
      <div className="bg-white rounded-lg shadow-sm">
        {workflows.length === 0 && !jobsLoading && !configsLoading ? (
          <div className="p-12 text-center">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Workflow Configurations</h3>
            <p className="text-gray-500 mb-6">
              You haven't created any workflow configurations yet. Create your first workflow to get started.
            </p>
            <Button onClick={handleCreateNew}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Workflow
            </Button>
          </div>
        ) : (
          <DataTable
            data={workflows}
            columns={columns}
            loading={jobsLoading || configsLoading}
            onEdit={handleEdit}
            onView={handleView}
            onDelete={handleDelete}
            onStart={handleStartWorkflow}
          />
        )}
      </div>

      {/* Job Selection Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Workflow</DialogTitle>
            <DialogDescription>
              Select a job to create a workflow for. Only jobs without existing workflows are shown.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select Job</label>
              <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a job..." />
                </SelectTrigger>
                <SelectContent>
                  {availableJobs.map((job) => (
                    <SelectItem key={job._id} value={job._id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{job.title}</span>
                        <span className="text-sm text-gray-500">{job.department} • {job.location}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {availableJobs.length === 0 && (
              <div className="text-center py-4">
                <p className="text-gray-500">No jobs available for workflow creation.</p>
                <p className="text-sm text-gray-400">All jobs already have workflows configured.</p>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false)
                setSelectedJobId('')
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleJobSelection}
              disabled={!selectedJobId}
            >
              Create Workflow
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
