import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, Brain, FileText, Zap, Plus, Trash2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAssessmentTypes } from '@/hooks/useAssessments'
import { AIInterviewConfigForm } from './AIInterviewConfigForm'
import { ManualAssessmentConfigForm } from './ManualAssessmentConfigForm'
import { HybridAssessmentConfigForm } from './HybridAssessmentConfigForm'
import type {
  AssessmentType,
  DifficultyLevel,
  SequenceType,
  CreateAssessmentRequest,
  IAssessmentConfig
} from '@/types'

// Base schema for common fields
const baseSchema = z.object({
  jobId: z.string().min(1, 'Job ID is required'),
  stage: z.string().min(1, 'Stage is required'),
  assessmentType: z.enum(['ai-interview', 'manual', 'hybrid'] as const),
})

// AI Interview schema
const aiInterviewSchema = z.object({
  questions: z.array(z.object({
    topic: z.string().min(1, 'Topic is required'),
    questionText: z.string().min(1, 'Question text is required'),
    followUpQuestions: z.array(z.string()).optional(),
    difficulty: z.enum(['easy', 'medium', 'hard'] as const).optional(),
  })).min(1, 'At least one question is required'),
  isProctoringRequired: z.boolean(),
  interviewDuration: z.number().min(15).max(180).optional(),
  difficultyLevel: z.enum(['easy', 'medium', 'hard'] as const).optional(),
  topics: z.array(z.string()).optional(),
  maxRetries: z.number().min(0).max(3).optional(),
  recordingEnabled: z.boolean().optional(),
})

// Manual Assessment schema
const manualAssessmentSchema = z.object({
  assessmentPlatform: z.string().min(1, 'Assessment platform is required'),
  assessmentLink: z.string().url('Must be a valid URL'),
  timeLimit: z.number().min(15).max(480).optional(),
  passingScore: z.number().min(0).max(100).optional(),
  instructions: z.string().optional(),
  maxAttempts: z.number().min(1).max(5).optional(),
  autoGrading: z.boolean().optional(),
})

// Hybrid Assessment schema
const hybridAssessmentSchema = z.object({
  aiInterviewConfig: aiInterviewSchema,
  manualAssessmentConfig: manualAssessmentSchema,
  sequence: z.enum(['ai-first', 'manual-first', 'parallel'] as const),
  combinedPassingCriteria: z.object({
    aiWeight: z.number().min(0).max(100),
    manualWeight: z.number().min(0).max(100),
    minimumCombinedScore: z.number().min(0).max(100),
  }).optional(),
})

// Dynamic schema based on assessment type
const createAssessmentSchema = z.discriminatedUnion('assessmentType', [
  baseSchema.extend({
    assessmentType: z.literal('ai-interview'),
    aiInterviewConfig: aiInterviewSchema,
  }),
  baseSchema.extend({
    assessmentType: z.literal('manual'),
    manualAssessmentConfig: manualAssessmentSchema,
  }),
  baseSchema.extend({
    assessmentType: z.literal('hybrid'),
    hybridAssessmentConfig: hybridAssessmentSchema,
  }),
])

type AssessmentFormData = z.infer<typeof createAssessmentSchema>

interface AssessmentConfigFormProps {
  jobId: string
  stage: string
  initialData?: IAssessmentConfig
  onSubmit: (data: CreateAssessmentRequest) => void
  onCancel: () => void
  isLoading?: boolean
}

export function AssessmentConfigForm({
  jobId,
  stage,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false
}: AssessmentConfigFormProps) {
  const [selectedType, setSelectedType] = useState<AssessmentType>(
    initialData?.assessmentType || 'ai-interview' as AssessmentType
  )

  const { data: assessmentTypes, isLoading: typesLoading } = useAssessmentTypes()

  // Fallback assessment types if API is not available
  const fallbackAssessmentTypes = {
    'ai-interview': {
      name: 'AI Interview',
      description: 'Automated interview with AI-powered questions',
      requiredFields: ['questions', 'duration'],
      optionalFields: ['proctoring', 'recording'],
      fieldDefinitions: {}
    },
    'manual': {
      name: 'Manual Assessment',
      description: 'Manual assessment on external platform',
      requiredFields: ['platformUrl', 'timeLimit'],
      optionalFields: ['passingScore'],
      fieldDefinitions: {}
    },
    'hybrid': {
      name: 'Hybrid Assessment',
      description: 'Combination of AI interview and manual assessment',
      requiredFields: ['sequence'],
      optionalFields: ['weightings'],
      fieldDefinitions: {}
    }
  }

  // Get type-specific configuration with fallback
  const getTypeConfig = (type: AssessmentType) => {
    return assessmentTypes?.assessmentTypes?.[type] || fallbackAssessmentTypes[type] || {}
  }

  const form = useForm<AssessmentFormData>({
    resolver: zodResolver(createAssessmentSchema),
    defaultValues: {
      jobId,
      stage,
      assessmentType: selectedType,
      ...(initialData && getFormDataFromConfig(initialData)),
    },
  })

  const { register, handleSubmit, watch, setValue, formState: { errors } } = form

  // Watch assessment type to show conditional fields
  const watchedType = watch('assessmentType')

  useEffect(() => {
    if (watchedType !== selectedType) {
      setSelectedType(watchedType)
      // Clear type-specific fields when switching types
      if (watchedType === 'ai-interview') {
        setValue('manualAssessmentConfig', undefined as any)
        setValue('hybridAssessmentConfig', undefined as any)
      } else if (watchedType === 'manual') {
        setValue('aiInterviewConfig', undefined as any)
        setValue('hybridAssessmentConfig', undefined as any)
      } else if (watchedType === 'hybrid') {
        setValue('aiInterviewConfig', undefined as any)
        setValue('manualAssessmentConfig', undefined as any)
      }
    }
  }, [watchedType, selectedType, setValue])

  const onFormSubmit = (data: AssessmentFormData) => {
    onSubmit(data as CreateAssessmentRequest)
  }

  if (typesLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Assessment Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Assessment Type</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="assessmentType">Type</Label>
            <Select
              value={selectedType}
              onValueChange={(value: AssessmentType) => {
                setSelectedType(value)
                setValue('assessmentType', value)
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select assessment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ai-interview">
                  <div className="flex items-center space-x-2">
                    <Brain className="h-4 w-4" />
                    <span>AI Interview</span>
                  </div>
                </SelectItem>
                <SelectItem value="manual">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>Manual Assessment</span>
                  </div>
                </SelectItem>
                <SelectItem value="hybrid">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4" />
                    <span>Hybrid Assessment</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.assessmentType && (
              <p className="text-sm text-red-600 mt-1">{errors.assessmentType.message}</p>
            )}
          </div>

          {/* Type Description */}
          {getTypeConfig(selectedType)?.description && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {getTypeConfig(selectedType)?.description}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Conditional Configuration Forms */}
      {selectedType === 'ai-interview' && (
        <AIInterviewConfigForm 
          form={form} 
          assessmentTypes={assessmentTypes} 
        />
      )}

      {selectedType === 'manual' && (
        <ManualAssessmentConfigForm 
          form={form} 
          assessmentTypes={assessmentTypes} 
        />
      )}

      {selectedType === 'hybrid' && (
        <HybridAssessmentConfigForm 
          form={form} 
          assessmentTypes={assessmentTypes} 
        />
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : initialData ? 'Update Assessment' : 'Create Assessment'}
        </Button>
      </div>
    </form>
  )
}

// Helper function to convert config to form data
function getFormDataFromConfig(config: IAssessmentConfig): Partial<AssessmentFormData> {
  const base = {
    jobId: config.jobId.toString(),
    stage: config.stage,
    assessmentType: config.assessmentType,
  }

  if (config.assessmentType === 'ai-interview' && config.aiInterviewConfig) {
    return {
      ...base,
      aiInterviewConfig: config.aiInterviewConfig,
    }
  }

  if (config.assessmentType === 'manual' && config.manualAssessmentConfig) {
    return {
      ...base,
      manualAssessmentConfig: config.manualAssessmentConfig,
    }
  }

  if (config.assessmentType === 'hybrid' && config.hybridAssessmentConfig) {
    return {
      ...base,
      hybridAssessmentConfig: config.hybridAssessmentConfig,
    }
  }

  return base
}


