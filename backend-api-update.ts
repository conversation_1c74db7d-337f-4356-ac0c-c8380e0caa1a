// Add this PUT endpoint to your existing jobConfig API file
// Insert this after the existing GET /:jobId endpoint and before the PUT /:jobId/stage/:stage endpoint

.put("/:jobId", async ({ params: { jobId }, body }: { params: { jobId: string }; body: any }) => {
    try {
        const existingConfig = await JobConfigModel.findOne({ jobId });
        
        if (!existingConfig) {
            // If config doesn't exist, create a new one
            const newJobConfig = await JobConfigModel.create({
                ...body,
                jobId,
                createdAt: new Date(),
                updatedAt: new Date()
            });
            const configData = newJobConfig.toObject() as any;

            return {
                success: true,
                message: "Job configuration created successfully",
                data: {
                    _id: configData._id,
                    jobId: configData.jobId,
                    flow: configData.flow.map((flowItem: any) => ({
                        stage: flowItem.stage,
                        next: flowItem.next.map((nextItem: any) => ({
                            stage: nextItem.stage,
                            outcome: nextItem.outcome
                        }))
                    })),
                    stageConfig: configData.stageConfig.map((stageItem: any) => ({
                        stage: stageItem.stage,
                        action: {
                            agentId: stageItem.action.agentId,
                            outputs: stageItem.action.outputs,
                            params: stageItem.action.params
                        },
                        communicationChannel: stageItem.communicationChannel,
                        scheduling: stageItem.scheduling
                    })),
                    createdAt: configData.createdAt,
                    updatedAt: configData.updatedAt
                }
            };
        }

        // Update the existing configuration
        const updatedConfig = await JobConfigModel.findOneAndUpdate(
            { jobId },
            { 
                ...body,
                updatedAt: new Date()
            },
            { new: true }
        ).lean();

        if (!updatedConfig) {
            return { error: "Failed to update job configuration" };
        }

        // Format the response properly
        const configData = updatedConfig as any;
        return {
            success: true,
            message: "Job configuration updated successfully",
            data: {
                _id: configData._id,
                jobId: configData.jobId,
                flow: configData.flow.map((flowItem: any) => ({
                    stage: flowItem.stage,
                    next: flowItem.next.map((nextItem: any) => ({
                        stage: nextItem.stage,
                        outcome: nextItem.outcome
                    }))
                })),
                stageConfig: configData.stageConfig.map((stageItem: any) => ({
                    stage: stageItem.stage,
                    action: {
                        agentId: stageItem.action.agentId,
                        outputs: stageItem.action.outputs,
                        params: stageItem.action.params
                    },
                    communicationChannel: stageItem.communicationChannel,
                    scheduling: stageItem.scheduling
                })),
                createdAt: configData.createdAt,
                updatedAt: configData.updatedAt
            }
        };
    } catch (err: any) {
        return { error: "Error saving job configuration", details: err?.message || String(err) };
    }
})
