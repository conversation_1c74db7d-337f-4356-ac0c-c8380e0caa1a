import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

const publicCandidateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  resumeLink: z.string().url('Please enter a valid resume URL (Google Drive, Dropbox, etc.)').optional().or(z.literal('')),
  contactInfo: z.object({
    linkedin: z.string().url('Please enter a valid LinkedIn URL').optional().or(z.literal('')),
    github: z.string().url('Please enter a valid GitHub URL').optional().or(z.literal('')),
    address: z.string().optional()
  })
})

type PublicCandidateFormData = z.infer<typeof publicCandidateSchema>

interface PublicCandidateFormProps {
  onSubmit: (data: PublicCandidateFormData) => Promise<void>
  isLoading?: boolean
}

export function PublicCandidateForm({ onSubmit, isLoading = false }: PublicCandidateFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<PublicCandidateFormData>({
    resolver: zodResolver(publicCandidateSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      resumeLink: '',
      contactInfo: {
        linkedin: '',
        github: '',
        address: ''
      }
    }
  })



  const onFormSubmit = async (data: PublicCandidateFormData) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
      {/* Personal Information */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            {...register('name')}
            placeholder="Enter your full name"
            className={errors.name ? 'border-red-500' : ''}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            {...register('email')}
            placeholder="<EMAIL>"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="phone">Phone Number *</Label>
          <Input
            id="phone"
            type="tel"
            {...register('phone')}
            placeholder="+****************"
            className={errors.phone ? 'border-red-500' : ''}
          />
          {errors.phone && (
            <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="address">Address</Label>
          <Textarea
            id="address"
            {...register('contactInfo.address')}
            placeholder="Your current address"
            rows={2}
            className={errors.contactInfo?.address ? 'border-red-500' : ''}
          />
          {errors.contactInfo?.address && (
            <p className="text-sm text-red-500 mt-1">{errors.contactInfo.address.message}</p>
          )}
        </div>
      </div>

      {/* Resume Link */}
      <div>
        <Label htmlFor="resumeLink">Resume Link (Recommended)</Label>
        <Input
          id="resumeLink"
          {...register('resumeLink')}
          placeholder="https://drive.google.com/file/d/... or https://dropbox.com/..."
          className={errors.resumeLink ? 'border-red-500' : ''}
        />
        {errors.resumeLink && (
          <p className="text-sm text-red-500 mt-1">{errors.resumeLink.message}</p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          Please provide a public link to your resume (Google Drive, Dropbox, OneDrive, etc.).
          Make sure the link is publicly accessible so we can view your resume.
        </p>
      </div>

      {/* Social Links */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="linkedin">LinkedIn Profile</Label>
          <Input
            id="linkedin"
            {...register('contactInfo.linkedin')}
            placeholder="https://linkedin.com/in/yourprofile"
            className={errors.contactInfo?.linkedin ? 'border-red-500' : ''}
          />
          {errors.contactInfo?.linkedin && (
            <p className="text-sm text-red-500 mt-1">{errors.contactInfo.linkedin.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="github">GitHub Profile</Label>
          <Input
            id="github"
            {...register('contactInfo.github')}
            placeholder="https://github.com/yourusername"
            className={errors.contactInfo?.github ? 'border-red-500' : ''}
          />
          {errors.contactInfo?.github && (
            <p className="text-sm text-red-500 mt-1">{errors.contactInfo.github.message}</p>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full"
        disabled={isLoading}
      >
        {isLoading ? 'Submitting Application...' : 'Submit Application'}
      </Button>

      <p className="text-xs text-gray-500 text-center">
        By submitting this application, you agree to our terms and conditions.
      </p>
    </form>
  )
}
