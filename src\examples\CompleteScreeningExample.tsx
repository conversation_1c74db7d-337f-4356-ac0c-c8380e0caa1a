import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Phone, Bot, User } from 'lucide-react'

// Automated Screening Components
import { ScreeningCallButton, QuickScreeningCallButton } from '@/components/screening/ScreeningCallButton'
import { useTriggerScreeningCall } from '@/hooks/useScreeningCall'

// Manual Screening Components
import { ManualScreeningActions, QuickManualScreeningActions } from '@/components/screening/ManualScreeningActions'
import { useManualScreeningActions } from '@/hooks/useManualScreening'

import type { IJobConfig, ICandidate } from '@/types'

/**
 * Complete example showing both automated and manual screening integration
 */
export function CompleteScreeningExample() {
  // Mock data
  const [jobConfig] = useState<IJobConfig | null>({
    _id: 'config_123',
    jobId: 'job_456',
    flow: [
      {
        stage: 'veda-review',
        next: [{ stage: 'screening', outcome: 'qualified' }]
      },
      {
        stage: 'screening',
        next: [{ stage: 'assessment', outcome: 'pass' }]
      }
    ],
    stageConfig: [
      {
        stage: 'screening',
        action: {
          agentId: 'screeningAgent',
          outputs: ['pass', 'fail'],
          params: {
            screeningConfig: {
              questions: [
                {
                  id: 1,
                  question: 'Are you comfortable with remote work?',
                  type: 'yes-no' as const,
                  options: ['Yes', 'No'],
                  correctAnswer: 'Yes',
                  required: true
                },
                {
                  id: 2,
                  question: 'Do you have experience with React and TypeScript?',
                  type: 'yes-no' as const,
                  options: ['Yes', 'No'],
                  correctAnswer: 'Yes',
                  required: true
                }
              ],
              passingScore: 80,
              timeLimit: 15,
              allowRetries: false,
              maxRetries: 0
            }
          }
        },
        communicationChannel: 'PLIVO' as const,
        scheduling: { type: 'IMMEDIATE' as const }
      }
    ]
  })

  const [candidates] = useState<ICandidate[]>([
    {
      _id: 'candidate_1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      stage: 'screening',
      jobId: 'job_456',
      resumeLink: '',
      expectedSalary: 75000,
      source: 'website',
      contactInfo: {}
    },
    {
      _id: 'candidate_2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+0987654321',
      stage: 'screening',
      jobId: 'job_456',
      resumeLink: '',
      expectedSalary: 80000,
      source: 'referral',
      contactInfo: {}
    }
  ])

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Complete Screening Integration</h2>
        <p className="text-gray-600 mb-6">
          This example shows both automated and manual screening workflows.
        </p>
      </div>

      {/* Screening Methods Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-blue-600" />
              <span>Automated Screening</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Uses your <code>/trigger-screening-call</code> endpoint with configured questions.
              </p>
              <div className="text-xs bg-gray-50 p-2 rounded">
                <strong>Payload:</strong> questions, companyName, role, to
              </div>
              <Badge variant="outline" className="text-blue-600">
                Fully Automated
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5 text-green-600" />
              <span>Manual Screening</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Uses your <code>vedaScreening.route.ts</code> to record manual call results.
              </p>
              <div className="text-xs bg-gray-50 p-2 rounded">
                <strong>Payload:</strong> candidateId, status: "CALL_COMPLETED", screeningCleared: "true"/"false"
              </div>
              <Badge variant="outline" className="text-green-600">
                Manual + Recording
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Candidates with Both Screening Options */}
      <Card>
        <CardHeader>
          <CardTitle>Candidates - Screening Options</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="automated" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="automated">Automated Screening</TabsTrigger>
              <TabsTrigger value="manual">Manual Screening</TabsTrigger>
            </TabsList>
            
            <TabsContent value="automated" className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Trigger automated screening calls using configured questions.
              </div>
              {candidates.map((candidate) => (
                <div key={candidate._id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div>
                        <h3 className="font-medium">{candidate.name}</h3>
                        <p className="text-sm text-gray-600">{candidate.email}</p>
                        <p className="text-sm text-gray-600">{candidate.phone}</p>
                      </div>
                      <Badge variant="outline">
                        {candidate.stage}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <ScreeningCallButton
                      jobConfig={jobConfig}
                      candidatePhone={candidate.phone}
                      candidateName={candidate.name}
                    />
                    
                    <QuickScreeningCallButton
                      jobConfig={jobConfig}
                      candidatePhone={candidate.phone}
                      candidateName={candidate.name}
                    />
                  </div>
                </div>
              ))}
            </TabsContent>
            
            <TabsContent value="manual" className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Conduct manual screening calls and record results.
              </div>
              {candidates.map((candidate) => (
                <div key={candidate._id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-medium">{candidate.name}</h3>
                      <p className="text-sm text-gray-600">{candidate.email}</p>
                      <p className="text-sm text-gray-600">{candidate.phone}</p>
                    </div>
                    <Badge variant="outline">
                      {candidate.stage}
                    </Badge>
                  </div>
                  
                  {/* Full Manual Screening Actions */}
                  <ManualScreeningActions
                    candidate={candidate}
                    showCallButton={true}
                    variant="default"
                  />
                </div>
              ))}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Compact Integration Example */}
      <Card>
        <CardHeader>
          <CardTitle>Compact Integration (Table Row Style)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {candidates.map((candidate) => (
              <div key={candidate._id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex-1">
                  <span className="font-medium">{candidate.name}</span>
                  <span className="text-sm text-gray-600 ml-2">{candidate.phone}</span>
                </div>
                
                <div className="flex items-center space-x-4">
                  {/* Automated Screening */}
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-500">Auto:</span>
                    <QuickScreeningCallButton
                      jobConfig={jobConfig}
                      candidatePhone={candidate.phone}
                      candidateName={candidate.name}
                    />
                  </div>
                  
                  {/* Manual Screening */}
                  <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-500">Manual:</span>
                    <ManualScreeningActions
                      candidate={candidate}
                      variant="compact"
                      showCallButton={false}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Integration Summary */}
      <Card>
        <CardHeader>
          <CardTitle>API Integration Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Automated Screening API</h4>
            <div className="text-sm bg-gray-50 p-3 rounded">
              <p><strong>Endpoint:</strong> <code>POST /trigger-screening-call</code></p>
              <p><strong>Payload:</strong></p>
              <pre className="text-xs mt-1">{`{
  "questions": "[{...}]",  // JSON string of questions
  "companyName": "PlacedHQ",
  "role": "Software Developer",
  "to": "+1234567890"
}`}</pre>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Manual Screening API</h4>
            <div className="text-sm bg-gray-50 p-3 rounded">
              <p><strong>Endpoint:</strong> <code>POST /veda-screening</code> (vedaScreening.route.ts)</p>
              <p><strong>Payload:</strong></p>
              <pre className="text-xs mt-1">{`{
  "candidateId": "candidate_123",
  "status": "CALL_COMPLETED",
  "screeningCleared": "true"  // or "false"
}`}</pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
