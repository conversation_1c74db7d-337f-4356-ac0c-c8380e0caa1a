/**
 * Test script to verify single-stage workflow validation
 * This tests the updated validation logic to ensure:
 * 1. Single-stage workflows are valid without connections
 * 2. Multi-stage workflows still require connections
 */

import { useWorkflowStore } from './stores/workflowStore'

// Mock data for testing
const mockSingleStageNode = {
  id: 'veda-review-1',
  type: 'stage' as const,
  position: { x: 100, y: 100 },
  data: {
    stage: 'veda-review',
    label: 'Veda Review',
    agentId: 'reviewAgent',
    outputs: ['best', 'good', 'bad'],
    isConfigured: true,
    communicationChannel: 'EMAIL',
    params: {}
  }
}

const mockSecondStageNode = {
  id: 'screening-1',
  type: 'stage' as const,
  position: { x: 300, y: 100 },
  data: {
    stage: 'screening',
    label: 'Screening',
    agentId: 'screeningAgent',
    outputs: ['pass', 'fail'],
    isConfigured: true,
    communicationChannel: 'EMAIL',
    params: {}
  }
}

function testSingleStageValidation() {
  console.log('🧪 Testing Single-Stage Workflow Validation...\n')

  // Get store instance
  const store = useWorkflowStore.getState()

  // Test 1: Single configured stage should be valid
  console.log('Test 1: Single configured stage validation')
  store.set({
    nodes: [mockSingleStageNode],
    edges: [],
    currentJobConfig: null
  })

  const singleStageValidation = store.validateWorkflow()
  console.log('Single stage result:', singleStageValidation)
  
  if (singleStageValidation.isValid) {
    console.log('✅ PASS: Single-stage workflow is valid without connections\n')
  } else {
    console.log('❌ FAIL: Single-stage workflow should be valid without connections')
    console.log('Errors:', singleStageValidation.errors)
    console.log('')
  }

  // Test 2: Single unconfigured stage should be invalid (due to configuration, not connections)
  console.log('Test 2: Single unconfigured stage validation')
  const unconfiguredNode = {
    ...mockSingleStageNode,
    data: {
      ...mockSingleStageNode.data,
      isConfigured: false
    }
  }
  
  store.set({
    nodes: [unconfiguredNode],
    edges: [],
    currentJobConfig: null
  })

  const unconfiguredValidation = store.validateWorkflow()
  console.log('Unconfigured single stage result:', unconfiguredValidation)
  
  if (!unconfiguredValidation.isValid && unconfiguredValidation.errors.some(e => e.includes('need configuration'))) {
    console.log('✅ PASS: Single unconfigured stage is invalid due to configuration requirement\n')
  } else {
    console.log('❌ FAIL: Single unconfigured stage should be invalid due to configuration requirement')
    console.log('Errors:', unconfiguredValidation.errors)
    console.log('')
  }

  // Test 3: Multi-stage workflow without connections should be invalid
  console.log('Test 3: Multi-stage workflow without connections')
  store.set({
    nodes: [mockSingleStageNode, mockSecondStageNode],
    edges: [],
    currentJobConfig: null
  })

  const multiStageNoConnectionsValidation = store.validateWorkflow()
  console.log('Multi-stage without connections result:', multiStageNoConnectionsValidation)
  
  if (!multiStageNoConnectionsValidation.isValid && multiStageNoConnectionsValidation.errors.some(e => e.includes('not connected'))) {
    console.log('✅ PASS: Multi-stage workflow without connections is invalid\n')
  } else {
    console.log('❌ FAIL: Multi-stage workflow without connections should be invalid')
    console.log('Errors:', multiStageNoConnectionsValidation.errors)
    console.log('')
  }

  // Test 4: Multi-stage workflow with connections should be valid
  console.log('Test 4: Multi-stage workflow with connections')
  const mockEdge = {
    id: 'veda-review-1-screening-1',
    source: 'veda-review-1',
    target: 'screening-1',
    type: 'smoothstep',
    animated: true,
    label: 'best',
    style: { stroke: '#6B7280', strokeWidth: 2 }
  }

  store.set({
    nodes: [mockSingleStageNode, mockSecondStageNode],
    edges: [mockEdge],
    currentJobConfig: null
  })

  const multiStageWithConnectionsValidation = store.validateWorkflow()
  console.log('Multi-stage with connections result:', multiStageWithConnectionsValidation)
  
  if (multiStageWithConnectionsValidation.isValid) {
    console.log('✅ PASS: Multi-stage workflow with connections is valid\n')
  } else {
    console.log('❌ FAIL: Multi-stage workflow with connections should be valid')
    console.log('Errors:', multiStageWithConnectionsValidation.errors)
    console.log('')
  }

  console.log('🎯 Single-Stage Validation Tests Complete!')
}

// Export for use in development/testing
export { testSingleStageValidation }

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  console.log('Single-stage validation test ready. Call testSingleStageValidation() to run tests.')
}
