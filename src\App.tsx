import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@/contexts/AuthContext'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Error<PERSON>andler } from '@/components/ErrorHandler'
import { MainLayout } from '@/components/layout/MainLayout'
import { LoginPage } from '@/pages/auth/LoginPage'
import { Dashboard } from '@/pages/Dashboard'
import { OrganizationList } from '@/pages/organizations/OrganizationList'
import { RecruiterList } from '@/pages/recruiters/RecruiterList'

import { JobList } from '@/pages/jobs/JobList'
import { CandidateList } from '@/pages/candidates/CandidateList'
import { WorkflowList } from '@/pages/workflows/WorkflowList'
import { WorkflowEditor } from '@/pages/workflows/WorkflowEditor'
import { WorkflowView } from '@/pages/workflows/WorkflowView'

import { AssessmentList } from '@/pages/assessments/AssessmentList'
import { PublicJobApplication } from '@/pages/public/PublicJobApplication'
import { Toaster } from '@/components/ui/toast'
import './App.css'

function App() {
  return (
    <>
      <Routes>
        {/* Public routes - no authentication required */}
        <Route path="/apply/:jobId" element={<PublicJobApplication />} />

        {/* Authenticated routes */}
        <Route path="/*" element={
          <AuthProvider>
            <ErrorHandler />
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }>
                <Route index element={<Dashboard />} />
                <Route path="organizations" element={<OrganizationList />} />
                <Route path="recruiters" element={<RecruiterList />} />

                <Route path="jobs" element={<JobList />} />
                <Route path="candidates" element={<CandidateList />} />
                <Route path="workflows" element={<WorkflowList />} />
                <Route path="workflows/editor" element={<WorkflowEditor />} />
                <Route path="workflows/view/:jobId" element={<WorkflowView />} />
                <Route path="assessments" element={<AssessmentList />} />
                <Route path="settings" element={<div className="p-6">Settings page coming soon...</div>} />
              </Route>
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </AuthProvider>
        } />
      </Routes>
      <Toaster />
    </>
  )
}

export default App
