import { useState } from 'react'
import { Plus, <PERSON>tings, Eye, Edit, Trash2, Workflow } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DataTable } from '@/components/tables/DataTable'
import { JobConfigForm } from '@/components/forms/JobConfigForm'
import { JobConfigDetail } from './JobConfigDetail'
import { useJobConfigs, useCreateJobConfig, useUpdateJobConfig, useDeleteJobConfig, type JobConfigFormData } from '@/hooks/useJobConfig'
import type { IJobConfig } from '@/types'
import { useJobs } from '@/hooks/useJobs'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

export function JobConfigList() {
  const { toast } = useToast()
  const { data: jobConfigs, isLoading } = useJobConfigs()
  const { data: jobs } = useJobs()
  const createMutation = useCreateJobConfig()
  const updateMutation = useUpdateJobConfig()
  const deleteMutation = useDeleteJobConfig()

  const [selectedJobConfig, setSelectedJobConfig] = useState<IJobConfig | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingJobConfig, setEditingJobConfig] = useState<IJobConfig | null>(null)

  const handleCreate = async (data: JobConfigFormData) => {
    try {
      await createMutation.mutateAsync(data)
      setIsCreateDialogOpen(false)
      toast({
        title: "Success",
        description: "Job configuration created successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create job configuration",
        variant: "destructive",
      })
    }
  }

  const handleUpdate = async (data: JobConfigFormData) => {
    if (!editingJobConfig) return
    
    try {
      await updateMutation.mutateAsync({ id: editingJobConfig._id, data })
      setIsEditDialogOpen(false)
      setEditingJobConfig(null)
      toast({
        title: "Success",
        description: "Job configuration updated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update job configuration",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this job configuration?')) {
      return
    }

    try {
      await deleteMutation.mutateAsync(id)
      toast({
        title: "Success",
        description: "Job configuration deleted successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete job configuration",
        variant: "destructive",
      })
    }
  }

  const getJobTitle = (jobId: string) => {
    const job = jobs?.find(j => j._id === jobId)
    return job?.title || 'Unknown Job'
  }

  const columns = [
    {
      key: 'jobId',
      label: 'Job',
      sortable: true,
      render: (value: string) => (
        <div className="font-medium">
          {getJobTitle(value)}
        </div>
      ),
    },
    {
      key: 'flow',
      label: 'Stages',
      sortable: false,
      render: (value: any[]) => (
        <Badge variant="secondary">
          {value?.length || 0} stages
        </Badge>
      ),
    },
    {
      key: 'stageConfig',
      label: 'Configurations',
      sortable: false,
      render: (value: any[]) => (
        <Badge variant="outline">
          {value?.length || 0} configs
        </Badge>
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value: Date | string) => (
        <span className="text-sm text-gray-500">
          {formatDate(value)}
        </span>
      ),
    },
  ]

  if (selectedJobConfig) {
    return (
      <JobConfigDetail
        jobConfig={selectedJobConfig}
        onBack={() => setSelectedJobConfig(null)}
        onEdit={(config) => {
          setEditingJobConfig(config)
          setIsEditDialogOpen(true)
          setSelectedJobConfig(null)
        }}
        onDelete={handleDelete}
      />
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Job Configurations</h1>
          <p className="text-gray-600">
            Manage recruitment workflow configurations for your jobs
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
            <Plus className="h-4 w-4 mr-2" />
            Create Configuration
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Job Configuration</DialogTitle>
            </DialogHeader>
            <JobConfigForm
              mode="create"
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Workflow className="h-5 w-5 mr-2" />
            Job Configurations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={jobConfigs || []}
            loading={isLoading}
            onEdit={(id) => {
              const jobConfig = jobConfigs?.find(jc => jc._id === id)
              if (jobConfig) {
                setEditingJobConfig(jobConfig)
                setIsEditDialogOpen(true)
              }
            }}
            onDelete={handleDelete}
            onView={(id) => {
              const jobConfig = jobConfigs?.find(jc => jc._id === id)
              if (jobConfig) setSelectedJobConfig(jobConfig)
            }}
          />
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Job Configuration</DialogTitle>
          </DialogHeader>
          {editingJobConfig && (
            <JobConfigForm
              mode="edit"
              initialData={editingJobConfig}
              onSubmit={handleUpdate}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setEditingJobConfig(null)
              }}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
