/**
 * Test script to verify the workflow connection corruption fix
 * 
 * This script demonstrates the issue and verifies that the fix works correctly.
 * Run this in the browser console to test the workflow store behavior.
 */

import { useWorkflowStore } from './stores/workflowStore'
import type { IJobConfig } from './types'

// Sample API response data (like the one provided in the issue)
const apiResponseData: IJobConfig = {
  "_id": "688a4b27186f8e4a28fd0715",
  "jobId": "688a2c0a3359b722666835fe",
  "flow": [
    {
      "stage": "veda-review_1753893443982",
      "next": [
        {
          "stage": "screening_1753893450853",
          "outcome": "best"
        },
        {
          "stage": "screening_1753893450853",
          "outcome": "good"
        },
        {
          "stage": "rejected",
          "outcome": "bad"
        }
      ]
    },
    {
      "stage": "assessment_1753893465327",
      "next": [
        {
          "stage": "stop",
          "outcome": "pass"
        },
        {
          "stage": "rejected",
          "outcome": "fail"
        }
      ]
    },
    {
      "stage": "screening_1753893450853",
      "next": [
        {
          "stage": "assessment_1753893465327",
          "outcome": "pass"
        },
        {
          "stage": "rejected",
          "outcome": "fail"
        }
      ]
    }
  ],
  "stageConfig": [
    {
      "stage": "veda-review",
      "action": {
        "agentId": "reviewAgent",
        "outputs": ["best", "good", "bad"],
        "params": {
          "reviewCriteria": "Match candidate profile against job description keywords and basic qualifications."
        }
      },
      "communicationChannel": "EMAIL",
      "scheduling": {
        "type": "IMMEDIATE"
      }
    },
    {
      "stage": "screening",
      "action": {
        "agentId": "screeningAgent",
        "outputs": ["pass", "fail"],
        "params": {
          "screeningConfig": {
            "questions": [
              {
                "id": 1,
                "question": "Are you comfortable with remote work?",
                "type": "yes-no",
                "options": ["Yes", "No"],
                "correctAnswer": "Yes",
                "required": true
              },
              {
                "id": 2,
                "question": "Do you have experience with the required technologies?",
                "type": "yes-no",
                "options": ["Yes", "No"],
                "correctAnswer": "Yes",
                "required": true
              }
            ],
            "passingScore": 80,
            "timeLimit": 15,
            "allowRetries": false,
            "maxRetries": 0
          }
        }
      },
      "communicationChannel": "PLIVO",
      "scheduling": {
        "type": "IMMEDIATE"
      }
    },
    {
      "stage": "assessment",
      "action": {
        "agentId": "assessmentAgent",
        "outputs": ["pass", "fail"],
        "params": {
          "assessmentDetails": {
            "assessmentPlatform": "hackerRank",
            "assessmentLink": "https://hackerrank.com"
          },
          "assessmentType": "manual"
        }
      },
      "communicationChannel": "EMAIL",
      "scheduling": {
        "type": "IMMEDIATE"
      }
    }
  ],
  "createdAt": "2025-07-30T16:41:11.756Z",
  "updatedAt": "2025-07-30T16:41:11.756Z"
}

export function testWorkflowFix() {
  console.log('🧪 Testing Workflow Loading Fix')
  console.log('===============================')

  const store = useWorkflowStore.getState()

  // Test 1: Load API response data and verify it loads correctly
  console.log('\n📥 Test 1: Loading API response data...')
  store.loadWorkflow(apiResponseData)

  const loadedConfig = store.currentJobConfig
  const loadedNodes = store.nodes
  const loadedEdges = store.edges

  console.log('Loaded flow configuration:')
  loadedConfig?.flow.forEach(f => {
    console.log(`  ${f.stage} -> [${f.next?.map(n => `${n.outcome}:${n.stage}`).join(', ') || 'none'}]`)
  })

  console.log('\nLoaded nodes:')
  loadedNodes.forEach(n => {
    console.log(`  ${n.id} (stage: ${n.data.stage}, agent: ${n.data.agentId}, configured: ${n.data.isConfigured})`)
  })

  console.log('\nLoaded edges:')
  loadedEdges.forEach(e => {
    console.log(`  ${e.source} -> ${e.target} (${e.label})`)
  })

  // Test 2: Verify stage configurations are applied
  console.log('\n🔧 Test 2: Verifying stage configurations...')
  const stageConfigsApplied = loadedNodes.every(node => {
    const stageConfig = apiResponseData.stageConfig.find(sc => sc.stage === node.data.stage)
    if (!stageConfig) return false

    const isCorrect =
      node.data.agentId === stageConfig.action.agentId &&
      JSON.stringify(node.data.outputs) === JSON.stringify(stageConfig.action.outputs) &&
      node.data.communicationChannel === stageConfig.communicationChannel

    if (!isCorrect) {
      console.log(`❌ Stage config mismatch for ${node.data.stage}:`)
      console.log(`  Expected: agent=${stageConfig.action.agentId}, outputs=${JSON.stringify(stageConfig.action.outputs)}, channel=${stageConfig.communicationChannel}`)
      console.log(`  Actual: agent=${node.data.agentId}, outputs=${JSON.stringify(node.data.outputs)}, channel=${node.data.communicationChannel}`)
    }

    return isCorrect
  })

  if (stageConfigsApplied) {
    console.log('✅ SUCCESS: All stage configurations applied correctly!')
  } else {
    console.log('❌ FAILURE: Some stage configurations were not applied correctly!')
  }

  // Test 3: Verify visual edges are created
  console.log('\n🔗 Test 3: Verifying visual edges...')
  const expectedEdgeCount = apiResponseData.flow.reduce((count, flowConfig) => {
    return count + (flowConfig.next?.filter(n => n.stage !== 'stop' && n.stage !== 'rejected').length || 0)
  }, 0)

  const actualEdgeCount = loadedEdges.length

  if (actualEdgeCount === expectedEdgeCount) {
    console.log(`✅ SUCCESS: Expected ${expectedEdgeCount} edges, got ${actualEdgeCount}`)
  } else {
    console.log(`❌ FAILURE: Expected ${expectedEdgeCount} edges, got ${actualEdgeCount}`)
  }

  // Test 4: Save workflow and verify flow is preserved
  console.log('\n💾 Test 4: Saving workflow...')
  const savedConfig = store.saveWorkflow()

  const flowBefore = JSON.stringify(loadedConfig?.flow, null, 2)
  const flowAfter = JSON.stringify(savedConfig?.flow, null, 2)

  if (flowBefore === flowAfter) {
    console.log('✅ SUCCESS: Flow configuration preserved during save!')
  } else {
    console.log('❌ FAILURE: Flow configuration was corrupted during save!')
    console.log('Before:', flowBefore)
    console.log('After:', flowAfter)
  }

  console.log('\n🎉 Workflow loading test completed!')

  return {
    apiResponseData,
    loadedNodes,
    loadedEdges,
    stageConfigsApplied,
    edgeCountCorrect: actualEdgeCount === expectedEdgeCount,
    flowPreserved: flowBefore === flowAfter
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testWorkflowFix = testWorkflowFix
}
