import { useState } from 'react'
import { Users, ChevronDown, CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { useUpdateCandidateStage, type ICandidate } from '@/hooks/useCandidates'
import { useToast } from '@/hooks/use-toast'

interface BulkStageManagerProps {
  selectedCandidates: ICandidate[]
  onSelectionChange?: (candidates: ICandidate[]) => void
}

const stageOptions = [
  { 
    value: 'registered', 
    label: 'Registered', 
    color: 'bg-blue-100 text-blue-800',
    icon: Clock
  },
  { 
    value: 'veda-review', 
    label: 'Veda Review', 
    color: 'bg-purple-100 text-purple-800',
    icon: AlertCircle
  },
  { 
    value: 'screening', 
    label: 'Screening', 
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock
  },
  { 
    value: 'assessment', 
    label: 'Assessment', 
    color: 'bg-orange-100 text-orange-800',
    icon: Clock
  },
  { 
    value: 'interview', 
    label: 'Interview', 
    color: 'bg-indigo-100 text-indigo-800',
    icon: Clock
  },
  { 
    value: 'completed_success', 
    label: 'Completed - Success', 
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  { 
    value: 'completed_fail', 
    label: 'Completed - Failed', 
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  },
  { 
    value: 'workflow_terminated', 
    label: 'Workflow Terminated', 
    color: 'bg-gray-100 text-gray-800',
    icon: XCircle
  },
]

export function BulkStageManager({ selectedCandidates, onSelectionChange }: BulkStageManagerProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
  const [selectedStage, setSelectedStage] = useState<string | null>(null)
  const updateStageMutation = useUpdateCandidateStage()
  const { toast } = useToast()

  const handleStageSelect = (stage: string) => {
    setSelectedStage(stage)
    setIsConfirmDialogOpen(true)
  }

  const handleBulkUpdate = async () => {
    if (!selectedStage || selectedCandidates.length === 0) return

    setIsUpdating(true)
    const stageOption = stageOptions.find(s => s.value === selectedStage)
    
    try {
      // Update all selected candidates
      const updatePromises = selectedCandidates.map(candidate =>
        updateStageMutation.mutateAsync({
          id: candidate._id,
          stage: selectedStage
        })
      )

      await Promise.all(updatePromises)

      toast({
        title: "Bulk Update Successful",
        description: `Updated ${selectedCandidates.length} candidates to ${stageOption?.label}`,
      })

      // Clear selection after successful update
      onSelectionChange?.([])
    } catch (error) {
      toast({
        title: "Bulk Update Failed",
        description: "Some candidates could not be updated. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUpdating(false)
      setIsConfirmDialogOpen(false)
      setSelectedStage(null)
    }
  }

  const getStageDistribution = () => {
    const distribution: Record<string, number> = {}
    selectedCandidates.forEach(candidate => {
      distribution[candidate.stage] = (distribution[candidate.stage] || 0) + 1
    })
    return distribution
  }

  if (selectedCandidates.length === 0) {
    return null
  }

  const stageDistribution = getStageDistribution()
  const selectedStageOption = selectedStage ? stageOptions.find(s => s.value === selectedStage) : null

  return (
    <>
      <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-900">
              {selectedCandidates.length} candidate{selectedCandidates.length !== 1 ? 's' : ''} selected
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {Object.entries(stageDistribution).map(([stage, count]) => {
              const stageOption = stageOptions.find(s => s.value === stage)
              return (
                <Badge key={`stage-${stage}`} className={`${stageOption?.color || 'bg-gray-100 text-gray-800'} border-0 text-xs`}>
                  {stageOption?.label || stage}: {count}
                </Badge>
              )
            })}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSelectionChange?.([])}
          >
            Clear Selection
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
              disabled={isUpdating}
            >
              Update Stage
              <ChevronDown className="h-4 w-4 ml-1" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <div className="px-2 py-1.5 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Move all selected to:
              </div>
              <DropdownMenuSeparator />
              {stageOptions.map((stage) => {
                const StageIcon = stage.icon
                
                return (
                  <DropdownMenuItem
                    key={stage.value}
                    onClick={() => handleStageSelect(stage.value)}
                    disabled={isUpdating}
                  >
                    <StageIcon className="h-4 w-4 mr-2" />
                    <span>{stage.label}</span>
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Stage Update</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Are you sure you want to update <strong>{selectedCandidates.length} candidate{selectedCandidates.length !== 1 ? 's' : ''}</strong> to the following stage?
            </p>
            
            {selectedStageOption && (
              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <selectedStageOption.icon className="h-5 w-5" />
                <span className="font-medium">{selectedStageOption.label}</span>
              </div>
            )}

            <div className="space-y-2">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Selected Candidates:
              </p>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {selectedCandidates.map(candidate => (
                  <div key={candidate._id} className="flex items-center justify-between text-sm p-2 bg-white rounded border">
                    <span>{candidate.name}</span>
                    <Badge className="text-xs">
                      {stageOptions.find(s => s.value === candidate.stage)?.label || candidate.stage}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsConfirmDialogOpen(false)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkUpdate}
                disabled={isUpdating}
              >
                {isUpdating ? 'Updating...' : 'Update All'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
