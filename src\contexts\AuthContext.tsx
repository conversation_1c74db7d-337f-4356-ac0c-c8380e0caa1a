import React, { createContext, useContext, useEffect } from 'react'
import { useIsAuthenticated, initializeAuth, type AuthUser } from '@/hooks/useAuth'

interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  user: AuthUser | undefined
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading, user } = useIsAuthenticated()

  // Initialize auth token on mount
  useEffect(() => {
    initializeAuth()
  }, [])

  return (
    <AuthContext.Provider value={{ isAuthenticated, isLoading, user }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
