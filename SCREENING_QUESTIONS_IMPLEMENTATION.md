# Screening Questions Implementation Guide

This document explains how screening questions are integrated into your workflow system and how they connect to your `/trigger-screening-call` API endpoint.

## Overview

The implementation provides a complete flow from configuring screening questions in the workflow editor to triggering screening calls with those questions via your API.

## Architecture

### 1. Data Flow
```
Workflow Editor → Stage Configuration → Job Config → API Call
```

1. **Configuration**: Users configure screening questions in the WorkflowEditor's StageConfigPanel
2. **Storage**: Questions are stored in the job configuration's stage params
3. **Retrieval**: Questions are retrieved when triggering screening calls
4. **API Integration**: Questions are formatted as JSON strings for your API

### 2. Key Components

#### Types (`src/types/index.ts`)
- `IScreeningQuestion`: Defines the structure of screening questions
- `IScreeningConfig`: Configuration object containing questions and settings

#### Workflow Store (`src/stores/workflowStore.ts`)
- Enhanced with default screening questions structure
- Stores questions in `stageConfig.action.params.screeningConfig`

#### Stage Configuration Panel (`src/components/workflow/StageConfigPanel.tsx`)
- Added screening-specific UI for question management
- Supports different question types (yes-no, multiple-choice, text, rating)
- Validates and saves questions to stage configuration

#### Utility Functions (`src/utils/screeningUtils.ts`)
- `getScreeningQuestionsFromJobConfig()`: Retrieves questions from job config
- `formatQuestionsForAPI()`: Formats questions as JSON string for API
- `validateScreeningQuestions()`: Validates questions before API call

#### Hooks (`src/hooks/useScreeningCall.ts`)
- `useScreeningCall()`: Core hook for API integration
- `useTriggerScreeningCall()`: Hook for calls with job config questions
- `useTriggerCustomScreeningCall()`: Hook for calls with custom questions

#### UI Components (`src/components/screening/ScreeningCallButton.tsx`)
- `ScreeningCallButton`: Dialog-based screening call trigger
- `QuickScreeningCallButton`: Simple button for quick calls

## Usage Examples

### 1. Configure Questions in Workflow Editor

```typescript
// Questions are configured in the StageConfigPanel when editing a screening stage
// The UI allows adding/editing questions with:
// - Question text
// - Question type (yes-no, multiple-choice, text, rating)
// - Options (for multiple-choice)
// - Correct answers (for scoring)
// - Required flag
```

### 2. Trigger Screening Call with Job Config

```typescript
import { useTriggerScreeningCall } from '@/hooks/useScreeningCall'

function CandidateRow({ candidate, jobConfig }) {
  const { triggerCall, isLoading } = useTriggerScreeningCall()

  const handleScreeningCall = async () => {
    await triggerCall(jobConfig, candidate.phone)
  }

  return (
    <Button onClick={handleScreeningCall} disabled={isLoading}>
      {isLoading ? 'Calling...' : 'Start Screening Call'}
    </Button>
  )
}
```

### 3. Trigger Call with Custom Questions

```typescript
import { useTriggerCustomScreeningCall } from '@/hooks/useScreeningCall'

function CustomScreeningCall() {
  const { triggerCall } = useTriggerCustomScreeningCall()

  const customQuestions = [
    {
      id: 1,
      question: 'Are you available for immediate start?',
      type: 'yes-no',
      options: ['Yes', 'No'],
      correctAnswer: 'Yes',
      required: true
    }
  ]

  const handleCall = async () => {
    await triggerCall(
      customQuestions,
      '+1234567890',
      'PlacedHQ',
      'Software Developer'
    )
  }

  return <Button onClick={handleCall}>Custom Screening Call</Button>
}
```

## API Integration

### Request Format
Your `/trigger-screening-call` endpoint receives:

```typescript
{
  questions: string,      // JSON stringified array of questions
  companyName: string,    // Company name
  role: string,          // Job role
  to: string             // Candidate phone number
}
```

### Questions JSON Structure
The `questions` parameter contains a JSON string of this structure:

```json
[
  {
    "id": 1,
    "question": "Are you comfortable with remote work?",
    "type": "yes-no",
    "options": ["Yes", "No"],
    "correctAnswer": "Yes",
    "required": true
  },
  {
    "id": 2,
    "question": "Rate your experience with React (1-5)",
    "type": "rating",
    "options": ["1", "2", "3", "4", "5"],
    "required": true
  }
]
```

## Configuration Storage

Questions are stored in the job configuration as:

```typescript
{
  stageConfig: [
    {
      stage: 'screening',
      action: {
        agentId: 'screeningAgent',
        outputs: ['pass', 'fail'],
        params: {
          screeningConfig: {
            questions: [/* array of IScreeningQuestion */],
            passingScore: 80,
            timeLimit: 15,
            allowRetries: false,
            maxRetries: 0
          }
        }
      },
      communicationChannel: 'PLIVO',
      scheduling: { type: 'IMMEDIATE' }
    }
  ]
}
```

## Next Steps

1. **Test the Implementation**: Use the example components to test the flow
2. **Customize UI**: Modify the StageConfigPanel UI to match your design
3. **Enhance Validation**: Add more validation rules as needed
4. **Add Analytics**: Track screening call success rates and outcomes
5. **Integrate Results**: Connect screening call results back to candidate records

## Files Created/Modified

- `src/types/index.ts` - Added screening question types
- `src/stores/workflowStore.ts` - Enhanced with screening config
- `src/components/workflow/StageConfigPanel.tsx` - Added screening UI
- `src/utils/screeningUtils.ts` - Utility functions for questions
- `src/hooks/useScreeningCall.ts` - API integration hooks
- `src/components/screening/ScreeningCallButton.tsx` - UI components
- `src/examples/ScreeningIntegrationExample.tsx` - Usage examples
