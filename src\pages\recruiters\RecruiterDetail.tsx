import { useState } from 'react'
import { <PERSON><PERSON><PERSON>t, Edit, Trash2, Mail, Building, Calendar } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { RecruiterForm } from '@/components/forms/RecruiterForm'
import { useUpdateRecruiter, useDeleteRecruiter } from '@/hooks/useRecruiters'
import { formatDate } from '@/lib/utils'

// Temporary type definitions until import issues are fixed
interface IRecruiter {
  _id: string;
  name: string;
  email: string;
  organization: string;
  password: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface RecruiterFormData {
  name: string;
  email: string;
  organization: string;
  password: string;
}

interface RecruiterDetailProps {
  recruiter: IRecruiter;
  onBack: () => void;
  onEdit: (recruiter: IRecruiter) => void;
  onDelete: (id: string) => void;
}

export function RecruiterDetail({ recruiter, onBack, onEdit, onDelete }: RecruiterDetailProps) {
  const updateMutation = useUpdateRecruiter()
  const deleteMutation = useDeleteRecruiter()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const handleUpdate = async (data: RecruiterFormData) => {
    await updateMutation.mutateAsync({ id: recruiter._id, data })
    setIsEditDialogOpen(false)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this recruiter?')) {
      await onDelete(recruiter._id)
      onBack()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Recruiters
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{recruiter.name}</h1>
            <p className="text-gray-600">Recruiter Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setIsEditDialogOpen(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-gray-900">{recruiter.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{recruiter.email}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Organization</label>
                  <div className="flex items-center space-x-2">
                    <Building className="h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">Organization {recruiter.organization}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Created</label>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{formatDate(recruiter.createdAt)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">0</div>
                <div className="text-sm text-gray-500">Active Jobs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">0</div>
                <div className="text-sm text-gray-500">Candidates Hired</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">0</div>
                <div className="text-sm text-gray-500">Interviews Scheduled</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>No recent activity to display</p>
            <p className="text-sm">Activity will appear here once the recruiter starts managing jobs and candidates</p>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Recruiter</DialogTitle>
          </DialogHeader>
          <RecruiterForm
            mode="edit"
            initialData={recruiter}
            onSubmit={handleUpdate}
            onCancel={() => setIsEditDialogOpen(false)}
            isLoading={updateMutation.isPending}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
