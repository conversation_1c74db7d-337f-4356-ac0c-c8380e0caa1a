import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'

// Authentication types
export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResponse {
  token: string
}

export interface AuthUser {
  _id: string
  name: string
  email: string
  organization: {
    _id: string
    name: string
    address: string
    domain: string
    createdAt: string
    updatedAt: string
    __v: number
  }
}

// Authentication hooks
export function useLogin() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (credentials: LoginCredentials) =>
      api.post<AuthResponse>('/auth/login', credentials),
    onSuccess: (data) => {
      // The API sets the authToken cookie automatically
      // Store a flag in localStorage to indicate authentication
      localStorage.setItem('isAuthenticated', 'true')

      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: ['auth', 'user'] })

      toast({
        title: "Login successful",
        description: "Welcome back!",
      })
    },
    onError: (error: any) => {
      // Error handling is now managed by the global ErrorHandler
      // But we can still provide specific feedback for login
      const message = error.status === 401
        ? "Invalid email or password"
        : error.message || "Login failed. Please try again."

      toast({
        title: "Login failed",
        description: message,
        variant: "destructive",
      })
    },
  })
}

export function useLogout() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async () => {
      // Clear authentication flag
      localStorage.removeItem('isAuthenticated')

      // Clear all cached data
      queryClient.clear()

      // Note: The backend should handle clearing the authToken cookie
      // If needed, we could call a logout endpoint here
    },
    onSuccess: () => {
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      })
    },
  })
}

// Get current user (this would need to be implemented on the backend)
export function useCurrentUser() {
  return useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => api.get<AuthUser>('/auth/me'),
    retry: false,
    // Always try to get user info - let the backend handle authentication
    enabled: true,
  })
}

// Check if user is authenticated
export function useIsAuthenticated() {
  const { data: user, isLoading } = useCurrentUser()
  return {
    isAuthenticated: !!user,
    isLoading,
    user,
  }
}

// Initialize auth on app start
export function initializeAuth() {
  // With cookie-based auth, we don't need to set tokens manually
  // The browser will automatically include cookies in requests
}
