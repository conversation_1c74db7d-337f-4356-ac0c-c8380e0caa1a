import React, { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Brain, Plus, Trash2, MessageSquare, Clock, Shield, Video } from 'lucide-react'
import type { AssessmentTypesResponse, DifficultyLevel } from '@/types'

interface AIInterviewConfigFormProps {
  form: UseFormReturn<any>
  assessmentTypes?: AssessmentTypesResponse
}

export function AIInterviewConfigForm({ form, assessmentTypes }: AIInterviewConfigFormProps) {
  const { register, watch, setValue, formState: { errors } } = form
  const [questions, setQuestions] = useState(
    watch('aiInterviewConfig.questions') || [{ topic: '', questionText: '', followUpQuestions: [], difficulty: 'medium' }]
  )

  const addQuestion = () => {
    const newQuestions = [...questions, { topic: '', questionText: '', followUpQuestions: [], difficulty: 'medium' }]
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  const removeQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index)
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  const updateQuestion = (index: number, field: string, value: any) => {
    const newQuestions = [...questions]
    newQuestions[index] = { ...newQuestions[index], [field]: value }
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  const addFollowUpQuestion = (questionIndex: number) => {
    const newQuestions = [...questions]
    if (!newQuestions[questionIndex].followUpQuestions) {
      newQuestions[questionIndex].followUpQuestions = []
    }
    newQuestions[questionIndex].followUpQuestions!.push('')
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  const updateFollowUpQuestion = (questionIndex: number, followUpIndex: number, value: string) => {
    const newQuestions = [...questions]
    newQuestions[questionIndex].followUpQuestions![followUpIndex] = value
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  const removeFollowUpQuestion = (questionIndex: number, followUpIndex: number) => {
    const newQuestions = [...questions]
    newQuestions[questionIndex].followUpQuestions!.splice(followUpIndex, 1)
    setQuestions(newQuestions)
    setValue('aiInterviewConfig.questions', newQuestions)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-blue-600" />
          <span>AI Interview Configuration</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Questions Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Interview Questions</Label>
            <Button type="button" variant="outline" size="sm" onClick={addQuestion}>
              <Plus className="h-4 w-4 mr-2" />
              Add Question
            </Button>
          </div>

          {questions.map((question, questionIndex) => (
            <Card key={questionIndex} className="border-l-4 border-l-blue-500">
              <CardContent className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <Badge variant="outline">Question {questionIndex + 1}</Badge>
                  {questions.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeQuestion(questionIndex)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`question-${questionIndex}-topic`}>Topic</Label>
                    <Input
                      id={`question-${questionIndex}-topic`}
                      value={question.topic}
                      onChange={(e) => updateQuestion(questionIndex, 'topic', e.target.value)}
                      placeholder="e.g., JavaScript, Problem Solving"
                    />
                  </div>

                  <div>
                    <Label htmlFor={`question-${questionIndex}-difficulty`}>Difficulty</Label>
                    <Select
                      value={question.difficulty || 'medium'}
                      onValueChange={(value: DifficultyLevel) => updateQuestion(questionIndex, 'difficulty', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select difficulty" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Easy</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="hard">Hard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor={`question-${questionIndex}-text`}>Question Text</Label>
                  <Textarea
                    id={`question-${questionIndex}-text`}
                    value={question.questionText}
                    onChange={(e) => updateQuestion(questionIndex, 'questionText', e.target.value)}
                    placeholder="Enter the main interview question..."
                    rows={3}
                  />
                </div>

                {/* Follow-up Questions */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Follow-up Questions (Optional)</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => addFollowUpQuestion(questionIndex)}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Follow-up
                    </Button>
                  </div>

                  {question.followUpQuestions?.map((followUp, followUpIndex) => (
                    <div key={followUpIndex} className="flex items-center space-x-2">
                      <Input
                        value={followUp}
                        onChange={(e) => updateFollowUpQuestion(questionIndex, followUpIndex, e.target.value)}
                        placeholder="Follow-up question..."
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFollowUpQuestion(questionIndex, followUpIndex)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}

          {errors.aiInterviewConfig?.questions && (
            <p className="text-sm text-red-600">{errors.aiInterviewConfig.questions.message}</p>
          )}
        </div>

        <Separator />

        {/* Interview Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="interview-duration">Interview Duration (minutes)</Label>
              <Input
                id="interview-duration"
                type="number"
                min="15"
                max="180"
                {...register('aiInterviewConfig.interviewDuration', { valueAsNumber: true })}
                placeholder="45"
              />
              <p className="text-xs text-gray-500 mt-1">15-180 minutes</p>
            </div>

            <div>
              <Label htmlFor="difficulty-level">Overall Difficulty Level</Label>
              <Select
                value={watch('aiInterviewConfig.difficultyLevel') || undefined}
                onValueChange={(value: DifficultyLevel) => setValue('aiInterviewConfig.difficultyLevel', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">Easy</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="max-retries">Max Retries</Label>
              <Input
                id="max-retries"
                type="number"
                min="0"
                max="3"
                {...register('aiInterviewConfig.maxRetries', { valueAsNumber: true })}
                placeholder="1"
              />
              <p className="text-xs text-gray-500 mt-1">0-3 retries allowed</p>
            </div>
          </div>

          <div className="space-y-4">
            {/* Boolean Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Proctoring Required</span>
                  </Label>
                  <p className="text-xs text-gray-500">Monitor candidate during interview</p>
                </div>
                <Switch
                  checked={watch('aiInterviewConfig.isProctoringRequired') || false}
                  onCheckedChange={(checked) => setValue('aiInterviewConfig.isProctoringRequired', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="flex items-center space-x-2">
                    <Video className="h-4 w-4" />
                    <span>Recording Enabled</span>
                  </Label>
                  <p className="text-xs text-gray-500">Record interview session</p>
                </div>
                <Switch
                  checked={watch('aiInterviewConfig.recordingEnabled') ?? true}
                  onCheckedChange={(checked) => setValue('aiInterviewConfig.recordingEnabled', checked)}
                />
              </div>
            </div>

            {/* Topics */}
            <div>
              <Label htmlFor="topics">Topics (Optional)</Label>
              <Textarea
                id="topics"
                value={watch('aiInterviewConfig.topics')?.join(', ') || ''}
                onChange={(e) => {
                  const topics = e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                  setValue('aiInterviewConfig.topics', topics)
                }}
                placeholder="JavaScript, React, Node.js, Problem Solving"
                rows={2}
              />
              <p className="text-xs text-gray-500 mt-1">Comma-separated list of topics</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
