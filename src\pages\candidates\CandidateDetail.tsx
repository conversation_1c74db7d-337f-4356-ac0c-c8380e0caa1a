import { ArrowLeft, Edit, Trash2, User, Mail, Phone, MapPin, Briefcase, DollarSign, Calendar, ExternalLink, Github, Linkedin } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { CandidateStageManager } from '@/components/candidates/CandidateStageManager'
import { useJobs } from '@/hooks/useJobs'
import { formatDate } from '@/lib/utils'
import type { ICandidate } from '@/hooks/useCandidates'

interface CandidateDetailProps {
  candidate: ICandidate
  onBack: () => void
  onEdit: (candidate: ICandidate) => void
  onDelete: (id: string) => void
}

export function CandidateDetail({ candidate, onBack, onEdit, onDelete }: CandidateDetailProps) {
  const { data: jobs } = useJobs()

  const getJobDetails = (jobId: string) => {
    return jobs?.find(j => j._id === jobId)
  }

  const getStageColor = (stage: string) => {
    const stageColors: Record<string, string> = {
      'registered': 'bg-blue-100 text-blue-800',
      'veda-review': 'bg-purple-100 text-purple-800',
      'screening': 'bg-yellow-100 text-yellow-800',
      'assessment': 'bg-orange-100 text-orange-800',
      'interview': 'bg-indigo-100 text-indigo-800',
      'completed_success': 'bg-green-100 text-green-800',
      'completed_fail': 'bg-red-100 text-red-800',
      'workflow_terminated': 'bg-gray-100 text-gray-800',
    }
    return stageColors[stage] || 'bg-gray-100 text-gray-800'
  }

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      'registered': 'bg-blue-100 text-blue-800',
      'pending_schedule': 'bg-yellow-100 text-yellow-800',
      'queued': 'bg-purple-100 text-purple-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'awaiting_result': 'bg-indigo-100 text-indigo-800',
      'completed_success': 'bg-green-100 text-green-800',
      'completed_fail': 'bg-red-100 text-red-800',
      'workflow_terminated': 'bg-gray-100 text-gray-800',
      'error': 'bg-red-100 text-red-800',
    }
    return statusColors[status] || 'bg-gray-100 text-gray-800'
  }

  const jobDetails = getJobDetails(candidate.jobId)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Candidates
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{candidate.name}</h1>
            <p className="text-gray-600">Candidate Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => onEdit(candidate)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => onDelete(candidate._id)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-600">{candidate.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Phone</p>
                    <p className="text-sm text-gray-600">{candidate.phone}</p>
                  </div>
                </div>
                {candidate.contactInfo.address && (
                  <div className="flex items-center space-x-3 md:col-span-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Address</p>
                      <p className="text-sm text-gray-600">{candidate.contactInfo.address}</p>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Social Links */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-900">Professional Links</h4>
                <div className="flex flex-wrap gap-3">
                  <Button variant="outline" size="sm" asChild>
                    <a href={candidate.resumeLink} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Resume
                    </a>
                  </Button>
                  {candidate.contactInfo.linkedin && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={candidate.contactInfo.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="h-4 w-4 mr-2" />
                        LinkedIn
                      </a>
                    </Button>
                  )}
                  {candidate.contactInfo.github && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={candidate.contactInfo.github} target="_blank" rel="noopener noreferrer">
                        <Github className="h-4 w-4 mr-2" />
                        GitHub
                      </a>
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Job Application Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="h-5 w-5" />
                <span>Application Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {jobDetails && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">{jobDetails.title}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                    <div>Department: {jobDetails.department}</div>
                    <div>Location: {jobDetails.location}</div>
                    <div>Type: {jobDetails.jobType.replace('_', ' ').toUpperCase()}</div>
                    <div>Experience: {jobDetails.experienceLevel.toUpperCase()}</div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-900">Source</p>
                  <p className="text-sm text-gray-600">{candidate.source}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Source UID</p>
                  <p className="text-sm text-gray-600">{candidate.sourceUid}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Applied Date</p>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{formatDate(candidate.createdAt)}</span>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Last Updated</p>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{formatDate(candidate.updatedAt)}</span>
                  </div>
                </div>
              </div>

              {candidate.expectedSalary && (
                <div className="flex items-center space-x-3">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Expected Salary</p>
                    <p className="text-sm text-gray-600">${candidate.expectedSalary.toLocaleString()} USD</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle>Current Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <CandidateStageManager candidate={candidate} />
              <div>
                <p className="text-sm font-medium text-gray-900 mb-2">Status</p>
                <Badge className={`${getStatusColor(candidate.status)} border-0`}>
                  {candidate.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Stage Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Stage Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* This would be populated with actual stage history in a real application */}
                <div className="text-sm text-gray-600">
                  Stage progression timeline would be displayed here based on candidate's journey through the recruitment process.
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Registered - {formatDate(candidate.createdAt)}</span>
                  </div>
                  {candidate.stage !== 'registered' && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Current: {candidate.stage.replace('_', ' ').replace('-', ' ')}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Phone className="h-4 w-4 mr-2" />
                Schedule Call
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Interview
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
