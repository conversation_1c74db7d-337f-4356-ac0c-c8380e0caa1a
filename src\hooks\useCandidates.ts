import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import type { CSVCandidateData, CSVUploadResponse } from '@/types'

// Simplified type definitions
type StageStatus = "registered" | "pending_schedule" | "queued" | "in_progress" | "awaiting_result" | "completed_success" | "completed_fail" | "workflow_terminated" | "error"

export interface ContactInfo {
  email: string;
  phone?: string;
  linkedin?: string;
  github?: string;
  address?: string;
}

export interface ICandidate {
  _id: string;
  sourceUid: string;
  name: string;
  email: string;
  phone: string;
  resumeLink: string;
  jobId: string; // Reference to Job
  stage: string;
  expectedSalary?: number;
  contactInfo: ContactInfo;
  status: string;
  source: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy?: string;
}

export interface CandidateFormData {
  name: string;
  email: string;
  phone: string;
  resumeLink: string;
  jobId: string;
  stage: string;
  expectedSalary?: number;
  contactInfo: ContactInfo;
  status: string;
  source: string;
}

export function useCandidates() {
  return useQuery({
    queryKey: ['candidates'],
    queryFn: async () => {
      try {
        console.log('🔍 Fetching candidates from:', '/candidate')
        const result = await api.get<ICandidate[]>('/candidate')
        console.log('✅ Candidates fetched successfully:', result)
        return result
      } catch (error) {
        console.error('❌ Error fetching candidates:', error)
        throw error
      }
    },
  })
}

export function useCandidate(id: string) {
  return useQuery({
    queryKey: ['candidates', id],
    queryFn: () => api.get<ICandidate>(`/candidate/${id}`),
    enabled: !!id,
  })
}

export function useCreateCandidate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CandidateFormData) =>
      api.post<ICandidate>('/candidate', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

export function useUpdateCandidate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: CandidateFormData }) =>
      api.put<ICandidate>(`/candidate/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

export function useDeleteCandidate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => api.delete(`/candidate/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Search candidates by name or email
export function useSearchCandidates(params: { name?: string; email?: string }) {
  return useQuery({
    queryKey: ['candidates', 'search', params],
    queryFn: () => api.get<ICandidate[]>('/candidate/search', params),
    enabled: !!(params.name || params.email),
  })
}

// Filter candidates by stage, status, or job
export function useFilterCandidates(params: { stage?: string; status?: string; jobId?: string }) {
  return useQuery({
    queryKey: ['candidates', 'filter', params],
    queryFn: () => api.get<ICandidate[]>('/candidate/filter', params),
    enabled: !!(params.stage || params.status || params.jobId),
  })
}

// Get candidates analytics
export function useCandidateAnalytics() {
  return useQuery({
    queryKey: ['candidates', 'analytics'],
    queryFn: () => api.get<{
      totalCandidates: number;
      candidatesByStage: Record<string, number>;
      candidatesByStatus: Record<string, number>;
    }>('/candidate/analytics'),
  })
}

// Update candidate stage
export function useUpdateCandidateStage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, stage }: { id: string; stage: string }) =>
      api.patch<ICandidate>(`/candidate/${id}/stage`, { stage }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Update candidate status
export function useUpdateCandidateStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      api.patch<ICandidate>(`/candidate/${id}/status`, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Get candidates for a specific job
export function useJobCandidates(jobId: string) {
  return useQuery({
    queryKey: ['candidates', 'job', jobId],
    queryFn: () => api.get<ICandidate[]>(`/candidate/job/${jobId}`),
    enabled: !!jobId,
  })
}

// CSV Template Download
export function useDownloadCSVTemplate() {
  return useMutation({
    mutationFn: async () => {
      // Create CSV template content directly for mock
      const csvTemplate = `name,email,phone,resumeLink,jobId,stage,expectedSalary,source,contactInfo.linkedin,contactInfo.github,contactInfo.address
John Doe,<EMAIL>,+1234567890,https://example.com/resume.pdf,job123,registered,75000,website,https://linkedin.com/in/johndoe,https://github.com/johndoe,123 Main St
Jane Smith,<EMAIL>,+0987654321,https://example.com/resume2.pdf,job456,interview,80000,referral,https://linkedin.com/in/janesmith,,456 Oak Ave`

      const blob = new Blob([csvTemplate], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'candidate_template.csv'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },
  })
}

// CSV Upload
export function useUploadCSVCandidates() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (csvData: CSVCandidateData[]) =>
      api.post<CSVUploadResponse>('/candidate/upload-csv', { csvData }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['candidates'] })
    },
  })
}

// Export types for use in components
export type { StageStatus }
