import { useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'

interface ManualScreeningRequest {
  candidateId: string
  status: 'CALL_COMPLETED'
  screeningCleared: 'true' | 'false'
}

interface ManualScreeningResponse {
  success: boolean
  message: string
  data?: any
}

/**
 * Hook for manual screening operations using vedaScreening.route.ts
 * This is used when screening calls are conducted manually and results need to be recorded
 */
export function useManualScreening() {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: ManualScreeningRequest): Promise<ManualScreeningResponse> => {
      // Ensure status is always CALL_COMPLETED for manual screening
      const requestData = {
        ...params,
        status: 'CALL_COMPLETED' as const
      }

      // Make API call to vedaScreening route
      return api.post<ManualScreeningResponse>('/veda-screening', requestData)
    },
    onSuccess: (data, variables) => {
      const passed = variables.screeningCleared === 'true'
      
      if (data.success) {
        toast({
          title: "Screening Result Recorded",
          description: `Candidate has been marked as ${passed ? 'passed' : 'failed'} for screening.`,
        })
        
        // Invalidate relevant queries to refresh candidate data
        queryClient.invalidateQueries({ queryKey: ['candidates'] })
        queryClient.invalidateQueries({ queryKey: ['candidate', variables.candidateId] })
      } else {
        toast({
          title: "Failed to Record Result",
          description: data.message || "Failed to record screening result.",
          variant: "destructive",
        })
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "An error occurred while recording the screening result.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook for marking a candidate as passed in manual screening
 */
export function usePassManualScreening() {
  const manualScreeningMutation = useManualScreening()

  const passCandidate = async (candidateId: string) => {
    return manualScreeningMutation.mutateAsync({
      candidateId,
      status: 'CALL_COMPLETED',
      screeningCleared: 'true'
    })
  }

  return {
    passCandidate,
    isLoading: manualScreeningMutation.isPending,
    error: manualScreeningMutation.error
  }
}

/**
 * Hook for marking a candidate as failed in manual screening
 */
export function useFailManualScreening() {
  const manualScreeningMutation = useManualScreening()

  const failCandidate = async (candidateId: string) => {
    return manualScreeningMutation.mutateAsync({
      candidateId,
      status: 'CALL_COMPLETED',
      screeningCleared: 'false'
    })
  }

  return {
    failCandidate,
    isLoading: manualScreeningMutation.isPending,
    error: manualScreeningMutation.error
  }
}

/**
 * Combined hook for both pass and fail operations
 */
export function useManualScreeningActions() {
  const manualScreeningMutation = useManualScreening()

  const recordResult = async (candidateId: string, passed: boolean) => {
    return manualScreeningMutation.mutateAsync({
      candidateId,
      status: 'CALL_COMPLETED',
      screeningCleared: passed ? 'true' : 'false'
    })
  }

  const passCandidate = async (candidateId: string) => {
    return recordResult(candidateId, true)
  }

  const failCandidate = async (candidateId: string) => {
    return recordResult(candidateId, false)
  }

  return {
    recordResult,
    passCandidate,
    failCandidate,
    isLoading: manualScreeningMutation.isPending,
    error: manualScreeningMutation.error
  }
}

// Export types for use in components
export type { ManualScreeningRequest, ManualScreeningResponse }
