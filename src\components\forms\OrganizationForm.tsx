import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { z } from "zod"

// Temporary type definitions until import issues are fixed
interface IOrganization {
  _id: string;
  name: string;
  address?: string;
  domain: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  address?: string;
}

// Temporary schema definition
const organizationSchema = z.object({
  name: z.string().min(1, "Organization name is required").max(100, "Name must be less than 100 characters"),
  domain: z.string()
    .min(1, "Domain is required")
    .regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/, "Please enter a valid domain (e.g., company.com)"),
  address: z.string().optional(),
})

interface OrganizationFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<IOrganization>
  onSubmit: (data: OrganizationFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function OrganizationForm({ 
  mode, 
  initialData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: OrganizationFormProps) {
  const { toast } = useToast()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: initialData?.name || "",
      domain: initialData?.domain || "",
      address: initialData?.address || "",
    },
  })

  const handleFormSubmit = async (data: OrganizationFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: `Organization ${mode === 'create' ? 'created' : 'updated'} successfully`,
      })
      if (mode === 'create') {
        reset()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode} organization. Please try again.`,
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Organization Name *</Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="Enter organization name"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="domain">Domain *</Label>
          <Input
            id="domain"
            {...register("domain")}
            placeholder="company.com"
            className={errors.domain ? "border-red-500" : ""}
          />
          {errors.domain && (
            <p className="text-sm text-red-500 mt-1">{errors.domain.message}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            Enter the company domain (e.g., company.com)
          </p>
        </div>

        <div>
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            {...register("address")}
            placeholder="Enter company address (optional)"
            className={errors.address ? "border-red-500" : ""}
          />
          {errors.address && (
            <p className="text-sm text-red-500 mt-1">{errors.address.message}</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting || isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            mode === 'create' ? 'Create Organization' : 'Update Organization'
          )}
        </Button>
      </div>
    </form>
  )
}