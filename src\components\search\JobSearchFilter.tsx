import { useState, useEffect, useCallback } from 'react'
import { SearchFilter } from './SearchFilter'
import type { <PERSON>Field, FilterField } from '@/types/search'
import { useSearchJobs, useFilterJobs, type IJob } from '@/hooks/useJobs'

interface JobSearchFilterProps {
  onResultsChange: (jobs: IJob[] | null) => void
  className?: string
}

export function JobSearchFilter({ onResultsChange, className }: JobSearchFilterProps) {
  const [searchParams, setSearchParams] = useState<{ title?: string; department?: string; location?: string }>({})
  const [filterParams, setFilterParams] = useState<{ 
    status?: string; 
    jobType?: string; 
    experienceLevel?: string; 
    workLocation?: string;
    department?: string;
  }>({})
  
  // Search and filter queries
  const { data: searchResults } = useSearchJobs(searchParams)
  const { data: filterResults } = useFilterJobs(filterParams)

  // Combine search and filter results
  useEffect(() => {
    const hasSearch = Object.values(searchParams).some(value => value)
    const hasFilter = Object.values(filterParams).some(value => value)

    if (hasSearch && hasFilter) {
      // If both search and filter are active, intersect results
      if (searchResults && filterResults) {
        const searchIds = new Set(searchResults.map(j => j._id))
        const intersected = filterResults.filter(j => searchIds.has(j._id))
        onResultsChange(intersected)
      } else {
        onResultsChange(searchResults || filterResults || null)
      }
    } else if (hasSearch) {
      onResultsChange(searchResults || null)
    } else if (hasFilter) {
      onResultsChange(filterResults || null)
    } else {
      onResultsChange(null) // Show all jobs
    }
  }, [searchResults, filterResults, searchParams, filterParams])

  const searchFields: SearchField[] = [
    {
      key: 'title',
      label: 'Job Title',
      placeholder: 'Search by job title...',
      type: 'text'
    },
    {
      key: 'department',
      label: 'Department',
      placeholder: 'Search by department...',
      type: 'text'
    },
    {
      key: 'location',
      label: 'Location',
      placeholder: 'Search by location...',
      type: 'text'
    }
  ]

  const filterFields: FilterField[] = [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'draft', label: 'Draft' },
        { value: 'closed', label: 'Closed' },
        { value: 'paused', label: 'Paused' }
      ]
    },
    {
      key: 'jobType',
      label: 'Job Type',
      type: 'select',
      options: [
        { value: 'full-time', label: 'Full Time' },
        { value: 'part-time', label: 'Part Time' },
        { value: 'contract', label: 'Contract' },
        { value: 'internship', label: 'Internship' },
        { value: 'freelance', label: 'Freelance' }
      ]
    },
    {
      key: 'experienceLevel',
      label: 'Experience Level',
      type: 'select',
      options: [
        { value: 'entry', label: 'Entry Level' },
        { value: 'mid', label: 'Mid Level' },
        { value: 'senior', label: 'Senior Level' },
        { value: 'lead', label: 'Lead' },
        { value: 'executive', label: 'Executive' }
      ]
    },
    {
      key: 'workLocation',
      label: 'Work Location',
      type: 'select',
      options: [
        { value: 'remote', label: 'Remote' },
        { value: 'onsite', label: 'On-site' },
        { value: 'hybrid', label: 'Hybrid' }
      ]
    }
  ]

  const handleSearchChange = useCallback((params: Record<string, string>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '')
    )
    setSearchParams(filteredParams)
  }, [])

  const handleFilterChange = useCallback((params: Record<string, string | string[]>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) =>
        Array.isArray(value) ? value.length > 0 : value !== ''
      )
    )
    setFilterParams(filteredParams)
  }, [])

  const handleClear = useCallback(() => {
    setSearchParams({})
    setFilterParams({})
  }, [])

  return (
    <SearchFilter
      searchFields={searchFields}
      filterFields={filterFields}
      onSearchChange={handleSearchChange}
      onFilterChange={handleFilterChange}
      onClear={handleClear}
      className={className}
    />
  )
}
