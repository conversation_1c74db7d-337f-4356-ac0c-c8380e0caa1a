import { useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { AlertTriangle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { WorkflowCanvas } from '@/components/workflow/WorkflowCanvas'
import { StageConfigPanel } from '@/components/workflow/StageConfigPanel'
import { useWorkflowStore } from '@/stores/workflowStore'
import { useJobs } from '@/hooks/useJobs'
import { useJobConfig } from '@/hooks/useJobConfig'

export function WorkflowEditor() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const jobId = searchParams.get('jobId')

  const { data: jobs, isLoading: jobsLoading } = useJobs()
  const { data: jobConfig, isLoading: configLoading } = useJobConfig(jobId || '')

  const {
    loadWorkflow,
    createNewWorkflow
  } = useWorkflowStore()

  // Load workflow configuration for the selected job
  useEffect(() => {
    if (jobId && jobs && !configLoading) {
      const job = jobs.find(j => j._id === jobId)
      console.log('WorkflowEditor useEffect:', { jobId, job: !!job, jobConfig, configLoading })

      if (job) {
        if (jobConfig && jobConfig !== null) {
          console.log('Loading existing job configuration:', jobConfig)
          // Load existing job configuration
          loadWorkflow(jobConfig)
        } else {
          // No configuration exists, create a new blank workflow
          console.log('No job configuration found, creating new workflow for job:', jobId)
          createNewWorkflow(jobId)
        }
      } else {
        console.log('Job not found for jobId:', jobId)
      }
    }
  }, [jobId, jobs, jobConfig, configLoading, loadWorkflow, createNewWorkflow])

  const selectedJob = jobs?.find(j => j._id === jobId)

  // Show loading state
  if (jobsLoading || configLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading workflow...</p>
        </div>
      </div>
    )
  }

  // Show error if no job ID provided
  if (!jobId) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              <span>No Job Selected</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Please select a job from the workflows page to edit its workflow.
            </p>
            <Button onClick={() => navigate('/workflows')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Main Content */}
      <div className="flex-1 flex">
        <div className="flex-1 relative">
          <WorkflowCanvas jobId={jobId!} />
        </div>
        {/* Stage Configuration Panel */}
        <StageConfigPanel />
      </div>
    </div>
  )
}
