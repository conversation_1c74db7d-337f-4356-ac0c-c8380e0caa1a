import { useMutation } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import { 
  getScreeningQuestionsFromJobConfig, 
  formatQuestionsForAPI, 
  getJobDetailsFromConfig,
  validateScreeningQuestions 
} from '@/utils/screeningUtils'
import type { IJobConfig, IScreeningQuestion } from '@/types'

interface TriggerScreeningCallRequest {
  questions: string
  companyName: string
  role: string
  to: string
}

interface TriggerScreeningCallResponse {
  status: boolean
  message: string
}

interface UseScreeningCallParams {
  jobConfig: IJobConfig | null
  candidatePhone: string
  customQuestions?: IScreeningQuestion[]
}

/**
 * Hook for triggering screening calls with proper question formatting
 */
export function useScreeningCall() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (params: UseScreeningCallParams): Promise<TriggerScreeningCallResponse> => {
      const { jobConfig, candidatePhone, customQuestions } = params

      // Get questions from either custom questions or job config
      const questions = customQuestions || getScreeningQuestionsFromJobConfig(jobConfig)
      
      // Validate questions
      const validation = validateScreeningQuestions(questions)
      if (!validation.isValid) {
        throw new Error(`Invalid questions: ${validation.errors.join(', ')}`)
      }

      // Format questions for API
      const formattedQuestions = formatQuestionsForAPI(questions)
      
      // Get job details
      const { companyName, role } = getJobDetailsFromConfig(jobConfig)

      // Prepare API request
      const requestData: TriggerScreeningCallRequest = {
        questions: formattedQuestions,
        companyName,
        role,
        to: candidatePhone
      }

      // Make API call
      return api.post<TriggerScreeningCallResponse>('/trigger-screening-call', requestData)
    },
    onSuccess: (data) => {
      if (data.status) {
        toast({
          title: "Screening Call Initiated",
          description: data.message || "The screening call has been successfully initiated.",
        })
      } else {
        toast({
          title: "Screening Call Failed",
          description: data.message || "Failed to initiate the screening call.",
          variant: "destructive",
        })
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "An error occurred while initiating the screening call.",
        variant: "destructive",
      })
    },
  })
}

/**
 * Hook for triggering screening calls with questions from job configuration
 */
export function useTriggerScreeningCall() {
  const screeningCallMutation = useScreeningCall()

  const triggerCall = async (jobConfig: IJobConfig | null, candidatePhone: string) => {
    return screeningCallMutation.mutateAsync({
      jobConfig,
      candidatePhone
    })
  }

  return {
    triggerCall,
    isLoading: screeningCallMutation.isPending,
    error: screeningCallMutation.error
  }
}

/**
 * Hook for triggering screening calls with custom questions
 */
export function useTriggerCustomScreeningCall() {
  const screeningCallMutation = useScreeningCall()

  const triggerCall = async (
    questions: IScreeningQuestion[], 
    candidatePhone: string, 
    companyName?: string, 
    role?: string
  ) => {
    // Create a mock job config with custom details if provided
    const mockJobConfig = companyName && role ? {
      _id: 'custom',
      jobId: 'custom',
      flow: [],
      stageConfig: [],
      // Add custom company and role data here if your structure supports it
    } as IJobConfig : null

    return screeningCallMutation.mutateAsync({
      jobConfig: mockJobConfig,
      candidatePhone,
      customQuestions: questions
    })
  }

  return {
    triggerCall,
    isLoading: screeningCallMutation.isPending,
    error: screeningCallMutation.error
  }
}

// Export types for use in components
export type { TriggerScreeningCallRequest, TriggerScreeningCallResponse, UseScreeningCallParams }
