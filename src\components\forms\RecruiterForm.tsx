import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { Eye, EyeOff } from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { useOrganizations } from "@/hooks/useOrganizations"
import { z } from "zod"

// Temporary type definitions until import issues are fixed
interface IRecruiter {
  _id: string;
  name: string;
  email: string;
  organization: string;
  password: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface RecruiterFormData {
  name: string;
  email: string;
  organization: string;
  password: string;
}

// Temporary schema definition
const recruiterSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Please enter a valid email address"),
  organization: z.string().min(1, "Organization is required"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
})

interface RecruiterFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<IRecruiter>
  onSubmit: (data: RecruiterFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function RecruiterForm({ 
  mode, 
  initialData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: RecruiterFormProps) {
  const { toast } = useToast()
  const { data: organizations } = useOrganizations()
  const [showPassword, setShowPassword] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<RecruiterFormData>({
    resolver: zodResolver(recruiterSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      organization: initialData?.organization?.toString() || "",
      password: "",
    },
  })

  const password = watch("password")
  
  // Password strength indicator
  const getPasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    if (/[^a-zA-Z\d]/.test(password)) strength++
    return strength
  }

  const passwordStrength = getPasswordStrength(password || "")
  const strengthLabels = ["Very Weak", "Weak", "Fair", "Good", "Strong"]
  const strengthColors = ["bg-red-500", "bg-orange-500", "bg-yellow-500", "bg-blue-500", "bg-green-500"]

  const handleFormSubmit = async (data: RecruiterFormData) => {
    try {
      await onSubmit(data)
      toast({
        title: "Success",
        description: `Recruiter ${mode === 'create' ? 'created' : 'updated'} successfully`,
      })
      if (mode === 'create') {
        reset()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode} recruiter. Please try again.`,
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            {...register("name")}
            placeholder="Enter full name"
            className={errors.name ? "border-red-500" : ""}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="Enter email address"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="organization">Organization *</Label>
          <Select
            onValueChange={(value: string) => setValue("organization", value)}
            defaultValue={initialData?.organization?.toString()}
          >
            <SelectTrigger className={errors.organization ? "border-red-500" : ""}>
              <SelectValue placeholder="Select organization" />
            </SelectTrigger>
            <SelectContent>
              {organizations?.map((org) => (
                <SelectItem key={org._id.toString()} value={org._id.toString()}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.organization && (
            <p className="text-sm text-red-500 mt-1">{errors.organization.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="password">Password *</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              {...register("password")}
              placeholder="Enter password"
              className={errors.password ? "border-red-500 pr-10" : "pr-10"}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
          
          {/* Password strength indicator */}
          {password && (
            <div className="mt-2">
              <div className="flex space-x-1 mb-1">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className={`h-1 w-full rounded ${
                      i < passwordStrength ? strengthColors[passwordStrength - 1] : "bg-gray-200"
                    }`}
                  />
                ))}
              </div>
              <p className="text-xs text-gray-600">
                Strength: {strengthLabels[passwordStrength - 1] || "Very Weak"}
              </p>
            </div>
          )}
          
          {errors.password && (
            <p className="text-sm text-red-500 mt-1">{errors.password.message}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            Password must be at least 8 characters with uppercase, lowercase, and number
          </p>
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting || isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {mode === 'create' ? 'Creating...' : 'Updating...'}
            </>
          ) : (
            mode === 'create' ? 'Create Recruiter' : 'Update Recruiter'
          )}
        </Button>
      </div>
    </form>
  )
}