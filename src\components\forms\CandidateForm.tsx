import { useForm } from "react-hook-form"
import { useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useJobs } from "@/hooks/useJobs"
import { z } from "zod"
import type { ICandidate, CandidateFormData } from "@/hooks/useCandidates"

// Validation schema
const candidateSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  resumeLink: z.string().url("Invalid resume URL"),
  jobId: z.string().min(1, "Job selection is required"),
  stage: z.string().min(1, "Stage is required"),
  expectedSalary: z.number().min(0, "Expected salary must be positive").optional(),
  contactInfo: z.object({
    email: z.string().email("Invalid email address"),
    phone: z.string().optional(),
    linkedin: z.string().url("Invalid LinkedIn URL").optional().or(z.literal("")),
    github: z.string().url("Invalid GitHub URL").optional().or(z.literal("")),
    address: z.string().optional(),
  }),
  status: z.string().min(1, "Status is required"),
  source: z.string().min(1, "Source is required"),
})

interface CandidateFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<ICandidate>
  onSubmit: (data: CandidateFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

const stageOptions = [
  { value: 'registered', label: 'Registered' },
  { value: 'veda-review', label: 'Veda Review' },
  { value: 'screening', label: 'Screening' },
  { value: 'assessment', label: 'Assessment' },
  { value: 'interview', label: 'Interview' },
  { value: 'completed_success', label: 'Completed - Success' },
  { value: 'completed_fail', label: 'Completed - Failed' },
  { value: 'workflow_terminated', label: 'Workflow Terminated' },
]

const statusOptions = [
  { value: 'registered', label: 'Registered' },
  { value: 'pending_schedule', label: 'Pending Schedule' },
  { value: 'queued', label: 'Queued' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'awaiting_result', label: 'Awaiting Result' },
  { value: 'completed_success', label: 'Completed - Success' },
  { value: 'completed_fail', label: 'Completed - Failed' },
  { value: 'workflow_terminated', label: 'Workflow Terminated' },
  { value: 'error', label: 'Error' },
]

const sourceOptions = [
  { value: 'LinkedIn', label: 'LinkedIn' },
  { value: 'Company Website', label: 'Company Website' },
  { value: 'Indeed', label: 'Indeed' },
  { value: 'Glassdoor', label: 'Glassdoor' },
  { value: 'Referral', label: 'Referral' },
  { value: 'Job Board', label: 'Job Board' },
  { value: 'Other', label: 'Other' },
]

export function CandidateForm({ 
  mode, 
  initialData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: CandidateFormProps) {
  const { toast } = useToast()
  const { data: jobs } = useJobs()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
  } = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      resumeLink: initialData?.resumeLink || "",
      jobId: initialData?.jobId || "",
      stage: initialData?.stage || "registered",
      expectedSalary: initialData?.expectedSalary || undefined,
      contactInfo: {
        email: initialData?.contactInfo?.email || initialData?.email || "",
        phone: initialData?.contactInfo?.phone || initialData?.phone || "",
        linkedin: initialData?.contactInfo?.linkedin || "",
        github: initialData?.contactInfo?.github || "",
        address: initialData?.contactInfo?.address || "",
      },
      status: initialData?.status || "registered",
      source: initialData?.source || "Company Website",
    },
  })

  // Keep contactInfo.email in sync with main email field
  const emailValue = watch('email')
  useEffect(() => {
    if (emailValue) {
      setValue('contactInfo.email', emailValue)
    }
  }, [emailValue, setValue])

  const handleFormSubmit = async (data: CandidateFormData) => {
    console.log('handleFormSubmit called with data:', data)
    console.log('Form errors:', errors)
    console.log('isSubmitting:', isSubmitting)
    console.log('isLoading:', isLoading)

    try {
      // Ensure contactInfo.email matches main email
      const cleanedData = {
        ...data,
        contactInfo: {
          ...data.contactInfo,
          email: data.email,
          phone: data.phone,
        }
      }

      console.log('Submitting candidate data:', cleanedData)
      await onSubmit(cleanedData)
      
      if (mode === 'create') {
        reset()
        toast({
          title: "Success",
          description: "Candidate created successfully",
        })
      } else {
        toast({
          title: "Success", 
          description: "Candidate updated successfully",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: mode === 'create' ? "Failed to create candidate" : "Failed to update candidate",
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="Enter candidate's full name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="<EMAIL>"
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="phone">Phone Number *</Label>
              <Input
                id="phone"
                {...register("phone")}
                placeholder="******-0123"
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && (
                <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="resumeLink">Resume URL *</Label>
              <Input
                id="resumeLink"
                {...register("resumeLink")}
                placeholder="https://example.com/resume.pdf"
                className={errors.resumeLink ? "border-red-500" : ""}
              />
              {errors.resumeLink && (
                <p className="text-sm text-red-500 mt-1">{errors.resumeLink.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job & Application Details */}
      <Card>
        <CardHeader>
          <CardTitle>Application Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="jobId">Applied Job *</Label>
              <Select onValueChange={(value) => setValue("jobId", value)} defaultValue={watch("jobId")}>
                <SelectTrigger className={errors.jobId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select a job" />
                </SelectTrigger>
                <SelectContent>
                  {jobs?.map((job) => (
                    <SelectItem key={job._id} value={job._id}>
                      {job.title} - {job.department}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.jobId && (
                <p className="text-sm text-red-500 mt-1">{errors.jobId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="source">Source *</Label>
              <Select onValueChange={(value) => setValue("source", value)} defaultValue={watch("source")}>
                <SelectTrigger className={errors.source ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  {sourceOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.source && (
                <p className="text-sm text-red-500 mt-1">{errors.source.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="stage">Current Stage *</Label>
              <Select onValueChange={(value) => setValue("stage", value)} defaultValue={watch("stage")}>
                <SelectTrigger className={errors.stage ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select stage" />
                </SelectTrigger>
                <SelectContent>
                  {stageOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.stage && (
                <p className="text-sm text-red-500 mt-1">{errors.stage.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status *</Label>
              <Select onValueChange={(value) => setValue("status", value)} defaultValue={watch("status")}>
                <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-500 mt-1">{errors.status.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="expectedSalary">Expected Salary (USD)</Label>
              <Input
                id="expectedSalary"
                type="number"
                {...register("expectedSalary", { valueAsNumber: true })}
                placeholder="75000"
                className={errors.expectedSalary ? "border-red-500" : ""}
              />
              {errors.expectedSalary && (
                <p className="text-sm text-red-500 mt-1">{errors.expectedSalary.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="linkedin">LinkedIn Profile</Label>
              <Input
                id="linkedin"
                {...register("contactInfo.linkedin")}
                placeholder="https://linkedin.com/in/username"
                className={errors.contactInfo?.linkedin ? "border-red-500" : ""}
              />
              {errors.contactInfo?.linkedin && (
                <p className="text-sm text-red-500 mt-1">{errors.contactInfo.linkedin.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="github">GitHub Profile</Label>
              <Input
                id="github"
                {...register("contactInfo.github")}
                placeholder="https://github.com/username"
                className={errors.contactInfo?.github ? "border-red-500" : ""}
              />
              {errors.contactInfo?.github && (
                <p className="text-sm text-red-500 mt-1">{errors.contactInfo.github.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              {...register("contactInfo.address")}
              placeholder="Enter full address"
              className={errors.contactInfo?.address ? "border-red-500" : ""}
              rows={3}
            />
            {errors.contactInfo?.address && (
              <p className="text-sm text-red-500 mt-1">{errors.contactInfo.address.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isLoading}
          onClick={() => console.log('Submit button clicked! isSubmitting:', isSubmitting, 'isLoading:', isLoading, 'errors:', errors)}
        >
          {isSubmitting || isLoading ? "Saving..." : mode === 'create' ? "Create Candidate" : "Update Candidate"}
        </Button>
      </div>
    </form>
  )
}
