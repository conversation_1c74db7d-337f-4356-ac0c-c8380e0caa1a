import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScreeningCallButton, QuickScreeningCallButton } from '@/components/screening/ScreeningCallButton'
import { useTriggerCustomScreeningCall } from '@/hooks/useScreeningCall'
import { getDefaultScreeningQuestions } from '@/utils/screeningUtils'
import type { IJobConfig, ICandidate } from '@/types'

// Example usage component showing how to integrate screening calls
export function ScreeningIntegrationExample() {
  // Mock data - replace with real data from your hooks
  const [jobConfig] = useState<IJobConfig | null>({
    _id: 'config_123',
    jobId: 'job_456',
    flow: [
      {
        stage: 'veda-review',
        next: [{ stage: 'screening', outcome: 'qualified' }]
      },
      {
        stage: 'screening',
        next: [{ stage: 'assessment', outcome: 'pass' }]
      }
    ],
    stageConfig: [
      {
        stage: 'screening',
        action: {
          agentId: 'screeningAgent',
          outputs: ['pass', 'fail'],
          params: {
            screeningConfig: {
              questions: [
                {
                  id: 1,
                  question: 'Are you comfortable with remote work?',
                  type: 'yes-no' as const,
                  options: ['Yes', 'No'],
                  correctAnswer: 'Yes',
                  required: true
                },
                {
                  id: 2,
                  question: 'Do you have experience with React and TypeScript?',
                  type: 'yes-no' as const,
                  options: ['Yes', 'No'],
                  correctAnswer: 'Yes',
                  required: true
                }
              ],
              passingScore: 80,
              timeLimit: 15,
              allowRetries: false,
              maxRetries: 0
            }
          }
        },
        communicationChannel: 'PLIVO' as const,
        scheduling: { type: 'IMMEDIATE' as const }
      }
    ]
  })

  const [candidates] = useState<ICandidate[]>([
    {
      _id: 'candidate_1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      stage: 'screening',
      jobId: 'job_456',
      resumeLink: '',
      expectedSalary: 75000,
      source: 'website',
      contactInfo: {}
    },
    {
      _id: 'candidate_2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+0987654321',
      stage: 'veda-review',
      jobId: 'job_456',
      resumeLink: '',
      expectedSalary: 80000,
      source: 'referral',
      contactInfo: {}
    }
  ])

  const { triggerCall: triggerCustomCall, isLoading: isCustomLoading } = useTriggerCustomScreeningCall()

  const handleCustomScreeningCall = async (candidatePhone: string) => {
    const customQuestions = getDefaultScreeningQuestions()
    
    try {
      await triggerCustomCall(
        customQuestions,
        candidatePhone,
        'PlacedHQ',
        'Software Developer'
      )
    } catch (error) {
      console.error('Custom screening call failed:', error)
    }
  }

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Screening Call Integration Examples</h2>
        <p className="text-gray-600 mb-6">
          This example shows how to integrate screening calls into your candidate management workflow.
        </p>
      </div>

      {/* Job Configuration Display */}
      <Card>
        <CardHeader>
          <CardTitle>Current Job Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          {jobConfig ? (
            <div className="space-y-2">
              <p><strong>Job ID:</strong> {jobConfig.jobId}</p>
              <p><strong>Screening Questions:</strong> {
                jobConfig.stageConfig
                  ?.find(stage => stage.stage === 'screening')
                  ?.action?.params?.screeningConfig?.questions?.length || 0
              }</p>
              <div className="mt-4">
                <h4 className="font-medium mb-2">Configured Questions:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {jobConfig.stageConfig
                    ?.find(stage => stage.stage === 'screening')
                    ?.action?.params?.screeningConfig?.questions?.map((q, index) => (
                      <li key={q.id}>
                        {index + 1}. {q.question} ({q.type})
                      </li>
                    ))}
                </ul>
              </div>
            </div>
          ) : (
            <p className="text-gray-500">No job configuration loaded</p>
          )}
        </CardContent>
      </Card>

      {/* Candidates List with Screening Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Candidates - Screening Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {candidates.map((candidate) => (
              <div key={candidate._id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div>
                      <h3 className="font-medium">{candidate.name}</h3>
                      <p className="text-sm text-gray-600">{candidate.email}</p>
                      <p className="text-sm text-gray-600">{candidate.phone}</p>
                    </div>
                    <Badge variant={candidate.stage === 'screening' ? 'default' : 'secondary'}>
                      {candidate.stage}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Standard Screening Call Button */}
                  <ScreeningCallButton
                    jobConfig={jobConfig}
                    candidatePhone={candidate.phone}
                    candidateName={candidate.name}
                    disabled={candidate.stage !== 'screening'}
                  />
                  
                  {/* Quick Screening Call Button */}
                  <QuickScreeningCallButton
                    jobConfig={jobConfig}
                    candidatePhone={candidate.phone}
                    candidateName={candidate.name}
                    disabled={candidate.stage !== 'screening'}
                  />
                  
                  {/* Custom Screening Call */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCustomScreeningCall(candidate.phone)}
                    disabled={isCustomLoading}
                  >
                    {isCustomLoading ? 'Calling...' : 'Custom Call'}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">1. Configure Screening Questions</h4>
            <p className="text-sm text-gray-600">
              Use the WorkflowEditor to configure screening questions for your job. Questions are stored in the stage configuration.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">2. Trigger Screening Calls</h4>
            <p className="text-sm text-gray-600">
              Use the ScreeningCallButton component or the useScreeningCall hook to trigger calls with configured questions.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">3. API Integration</h4>
            <p className="text-sm text-gray-600">
              Questions are automatically formatted as JSON strings and sent to your /trigger-screening-call endpoint.
            </p>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">4. Custom Questions</h4>
            <p className="text-sm text-gray-600">
              Use useTriggerCustomScreeningCall for one-off calls with custom questions not tied to job configurations.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
