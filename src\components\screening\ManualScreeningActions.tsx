import { useState } from 'react'
import { CheckCircle, XCircle, Phone, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { useManualScreeningActions } from '@/hooks/useManualScreening'
import type { ICandidate } from '@/types'

interface ManualScreeningActionsProps {
  candidate: ICandidate
  disabled?: boolean
  showCallButton?: boolean
  variant?: 'default' | 'compact' | 'dropdown'
}

/**
 * Component for manual screening actions - pass/fail candidates after manual screening calls
 */
export function ManualScreeningActions({
  candidate,
  disabled = false,
  showCallButton = true,
  variant = 'default'
}: ManualScreeningActionsProps) {
  const [isPassDialogOpen, setIsPassDialogOpen] = useState(false)
  const [isFailDialogOpen, setIsFailDialogOpen] = useState(false)
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false)
  
  const { passCandidate, failCandidate, isLoading } = useManualScreeningActions()

  const handlePass = async () => {
    try {
      await passCandidate(candidate._id)
      setIsPassDialogOpen(false)
    } catch (error) {
      console.error('Failed to pass candidate:', error)
    }
  }

  const handleFail = async () => {
    try {
      await failCandidate(candidate._id)
      setIsFailDialogOpen(false)
    } catch (error) {
      console.error('Failed to fail candidate:', error)
    }
  }

  // Compact variant for table rows
  if (variant === 'compact') {
    return (
      <div className="flex items-center space-x-1">
        {showCallButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsCallDialogOpen(true)}
            disabled={disabled}
          >
            <Phone className="h-3 w-3" />
          </Button>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsPassDialogOpen(true)}
          disabled={disabled || isLoading}
          className="text-green-600 hover:text-green-700 hover:bg-green-50"
        >
          <CheckCircle className="h-3 w-3" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsFailDialogOpen(true)}
          disabled={disabled || isLoading}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <XCircle className="h-3 w-3" />
        </Button>

        {/* Confirmation Dialogs */}
        <AlertDialog open={isPassDialogOpen} onOpenChange={setIsPassDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Pass Screening</AlertDialogTitle>
              <AlertDialogDescription>
                Mark {candidate.name} as passed for screening? This will move them to the next stage.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handlePass} disabled={isLoading}>
                {isLoading ? 'Processing...' : 'Pass Candidate'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AlertDialog open={isFailDialogOpen} onOpenChange={setIsFailDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Fail Screening</AlertDialogTitle>
              <AlertDialogDescription>
                Mark {candidate.name} as failed for screening? This will remove them from the current workflow.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleFail} disabled={isLoading}>
                {isLoading ? 'Processing...' : 'Fail Candidate'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Call Instructions Dialog */}
        <Dialog open={isCallDialogOpen} onOpenChange={setIsCallDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Manual Screening Call</DialogTitle>
              <DialogDescription>
                Call {candidate.name} manually for screening interview.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
                <Phone className="h-5 w-5 text-blue-600" />
                <span className="font-medium">{candidate.phone}</span>
              </div>
              <div className="text-sm text-gray-600">
                <p>After completing the call, use the Pass/Fail buttons to record the result.</p>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    )
  }

  // Default variant with full buttons
  return (
    <div className="space-y-3">
      {showCallButton && (
        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <Phone className="h-5 w-5 text-gray-600" />
          <div className="flex-1">
            <p className="text-sm font-medium">Manual Screening Call</p>
            <p className="text-xs text-gray-600">Call: {candidate.phone}</p>
          </div>
          <Badge variant="outline">
            <Clock className="h-3 w-3 mr-1" />
            Manual
          </Badge>
        </div>
      )}

      <div className="flex space-x-3">
        <Button
          onClick={() => setIsPassDialogOpen(true)}
          disabled={disabled || isLoading}
          className="flex-1 bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-4 w-4 mr-2" />
          Pass Screening
        </Button>
        
        <Button
          onClick={() => setIsFailDialogOpen(true)}
          disabled={disabled || isLoading}
          variant="destructive"
          className="flex-1"
        >
          <XCircle className="h-4 w-4 mr-2" />
          Fail Screening
        </Button>
      </div>

      {/* Confirmation Dialogs */}
      <AlertDialog open={isPassDialogOpen} onOpenChange={setIsPassDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Pass Screening</AlertDialogTitle>
            <AlertDialogDescription>
              Mark {candidate.name} as passed for screening? This will move them to the next stage in the workflow.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handlePass} disabled={isLoading}>
              {isLoading ? 'Processing...' : 'Pass Candidate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isFailDialogOpen} onOpenChange={setIsFailDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Fail Screening</AlertDialogTitle>
            <AlertDialogDescription>
              Mark {candidate.name} as failed for screening? This will remove them from the current workflow.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleFail} disabled={isLoading}>
              {isLoading ? 'Processing...' : 'Fail Candidate'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

/**
 * Simple pass/fail buttons for quick actions
 */
export function QuickManualScreeningActions({
  candidate,
  disabled = false
}: Pick<ManualScreeningActionsProps, 'candidate' | 'disabled'>) {
  const { passCandidate, failCandidate, isLoading } = useManualScreeningActions()

  return (
    <div className="flex items-center space-x-2">
      <Button
        size="sm"
        variant="outline"
        onClick={() => passCandidate(candidate._id)}
        disabled={disabled || isLoading}
        className="text-green-600 hover:text-green-700 hover:bg-green-50"
      >
        <CheckCircle className="h-4 w-4 mr-1" />
        Pass
      </Button>
      
      <Button
        size="sm"
        variant="outline"
        onClick={() => failCandidate(candidate._id)}
        disabled={disabled || isLoading}
        className="text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        <XCircle className="h-4 w-4 mr-1" />
        Fail
      </Button>
    </div>
  )
}
