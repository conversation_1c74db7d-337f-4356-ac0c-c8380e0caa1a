import { memo } from 'react'
import { <PERSON><PERSON>, <PERSON>si<PERSON>, type NodeProps } from 'reactflow'
import { <PERSON><PERSON>s, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useWorkflowStore, type WorkflowStageData } from '@/stores/workflowStore'

interface WorkflowStageNodeProps extends NodeProps {
  data: WorkflowStageData
}

export const WorkflowStageNode = memo(({ data, selected, id }: WorkflowStageNodeProps) => {
  const { selectNode, getNodeById } = useWorkflowStore()

  const handleClick = () => {
    const node = getNodeById(id)
    if (node) {
      selectNode(node)
    }
  }

  const getStageIcon = () => {
    if (data.isConfigured) {
      return <CheckCircle className="h-3 w-3 text-green-400" />
    }
    return <AlertCircle className="h-3 w-3 text-yellow-400" />
  }

  const getStageColor = () => {
    switch (data.stage) {
      case 'veda-review':
        return 'from-purple-500 to-purple-600'
      case 'screening':
        return 'from-amber-500 to-amber-600'
      case 'assessment':
        return 'from-red-500 to-red-600'
      case 'interview':
        return 'from-green-500 to-green-600'
      case 'final-review':
        return 'from-blue-500 to-blue-600'
      case 'offer':
        return 'from-indigo-500 to-indigo-600'
      case 'rejected':
        return 'from-gray-500 to-gray-600'
      case 'hired':
        return 'from-emerald-500 to-emerald-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  return (
    <div className="workflow-stage-node">
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-2 h-2 !bg-gray-400 border-1 border-white"
        style={{ left: -4 }}
      />
      
      {/* Node Content */}
      <div
        className={cn(
          "relative px-3 py-2 rounded-md shadow-md cursor-pointer transition-all duration-200",
          "bg-gradient-to-br text-white min-w-[160px] max-w-[180px]",
          getStageColor(),
          selected && "ring-1 ring-blue-400 ring-offset-1 ring-offset-white",
          "hover:shadow-lg hover:scale-102"
        )}
        onClick={handleClick}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-1">
            {getStageIcon()}
            <span className="text-[10px] font-medium opacity-90">
              {data.stage.toUpperCase()}
            </span>
          </div>
          <Settings className="h-2 w-2 opacity-70" />
        </div>

        {/* Stage Label */}
        <div className="text-xs font-semibold mb-1">
          {data.label}
        </div>
        
        {/* Configuration Status */}
        <div className="text-[10px] opacity-80">
          {data.isConfigured ? (
            <div className="flex items-center space-x-1">
              <span>Configured</span>
              {data.agentId && (
                <span className="bg-white/20 px-1 rounded text-[9px]">
                  {data.agentId}
                </span>
              )}
            </div>
          ) : (
            <span className="text-yellow-200">Needs Configuration</span>
          )}
        </div>
        
        {/* Communication Channdel Indicator */}
        {data.communicationChannel && (
          <div className="absolute -top-1 -right-1 bg-white text-gray-700 text-xs px-1 py-0.5 rounded-full text-[10px] font-medium">
            {data.communicationChannel}
          </div>
        )}
        
        {/* Outputs Preview */}
        {data.outputs && data.outputs.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            {data.outputs.slice(0, 2).map((output, index) => (
              <span
                key={index}
                className="bg-white/20 text-xs px-1 py-0.5 rounded text-[10px]"
              >
                {output}
              </span>
            ))}
            {data.outputs.length > 2 && (
              <span className="bg-white/20 text-xs px-1 py-0.5 rounded text-[10px]">
                +{data.outputs.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
      
      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
        style={{ right: -6 }}
      />
    </div>
  )
})

WorkflowStageNode.displayName = 'WorkflowStageNode'
