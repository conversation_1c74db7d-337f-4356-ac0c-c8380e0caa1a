import { useForm, useField<PERSON>rray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Plus, X, Trash2 } from "lucide-react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { useOrganizations } from "@/hooks/useOrganizations"
import { useAuth } from "@/contexts/AuthContext"
import { z } from "zod"

// Simplified type definitions
type JobType = "full_time" | "part_time" | "contract" | "internship"
type WorkLocation = "remote" | "onsite" | "hybrid"
type ExperienceLevel = "entry" | "mid" | "senior" | "lead" | "executive"
type JobStatus = "draft" | "active" | "paused" | "closed" | "cancelled"

interface RequiredSkill {
  name: string;
  level: "required" | "preferred" | "nice_to_have";
  yearsRequired?: number;
}

interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  period: "hourly" | "monthly" | "yearly";
}

interface IJob {
  _id: string;
  title: string;
  description: string;
  department: string;
  location: string;
  jobType: JobType;
  workLocation: WorkLocation;
  experienceLevel: ExperienceLevel;
  requiredSkills: RequiredSkill[];
  qualifications: string[];
  responsibilities: string[];
  salaryRange?: SalaryRange;
  status: JobStatus;
  openings: number;
  applicationDeadline?: Date | string;
  startDate?: Date | string;
  organization: string;
}

interface JobFormData {
  title: string;
  description: string;
  department: string;
  location: string;
  jobType: JobType;
  workLocation: WorkLocation;
  experienceLevel: ExperienceLevel;
  requiredSkills: RequiredSkill[];
  qualifications: string[];
  responsibilities: string[];
  salaryRange?: SalaryRange;
  status: JobStatus;
  openings: number;
  applicationDeadline?: Date;
  startDate?: Date;
  organization: string;
  createdBy?: string; // Optional for form, will be added automatically
}

// Validation schema
const requiredSkillSchema = z.object({
  name: z.string().min(1, "Skill name is required"),
  level: z.enum(["required", "preferred", "nice_to_have"]),
  yearsRequired: z.number().min(0).optional(),
})

const salaryRangeSchema = z.object({
  min: z.number().min(0, "Minimum salary must be positive"),
  max: z.number().min(0, "Maximum salary must be positive"),
  currency: z.string().min(1, "Currency is required"),
  period: z.enum(["hourly", "monthly", "yearly"]),
}).refine(data => data.max >= data.min, {
  message: "Maximum salary must be greater than or equal to minimum salary",
  path: ["max"],
})

const jobSchema = z.object({
  title: z.string().min(1, "Job title is required").max(100, "Title must be less than 100 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").max(5000, "Description must be less than 5000 characters"),
  department: z.string().min(1, "Department is required"),
  location: z.string().min(1, "Location is required"),
  jobType: z.enum(["full_time", "part_time", "contract", "internship"]),
  workLocation: z.enum(["remote", "onsite", "hybrid"]),
  experienceLevel: z.enum(["entry", "mid", "senior", "lead", "executive"]),
  requiredSkills: z.array(requiredSkillSchema).min(1, "At least one skill is required"),
  qualifications: z.array(z.string().min(1, "Qualification cannot be empty")).min(1, "At least one qualification is required"),
  responsibilities: z.array(z.string().min(1, "Responsibility cannot be empty")).min(1, "At least one responsibility is required"),
  salaryRange: salaryRangeSchema.optional(),
  status: z.enum(["draft", "active", "paused", "closed", "cancelled"]),
  openings: z.number().min(1, "Number of openings must be at least 1"),
  applicationDeadline: z.date().optional(),
  startDate: z.date().optional(),
  organization: z.string().min(1, "Organization is required"),
  createdBy: z.string().optional(), // Optional in form validation since it's added automatically
})

interface JobFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<IJob>
  onSubmit: (data: JobFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function JobForm({ 
  mode, 
  initialData, 
  onSubmit, 
  onCancel,
  isLoading = false 
}: JobFormProps) {
  const { toast } = useToast()
  const { data: organizations } = useOrganizations()
  const { user } = useAuth()
  const [includeSalary, setIncludeSalary] = useState(!!initialData?.salaryRange)
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    control,
    setValue,
    watch,
    reset,
  } = useForm<JobFormData>({
    resolver: zodResolver(jobSchema),
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      department: initialData?.department || "",
      location: initialData?.location || "",
      jobType: initialData?.jobType || "full_time",
      workLocation: initialData?.workLocation || "onsite",
      experienceLevel: initialData?.experienceLevel || "mid",
      requiredSkills: initialData?.requiredSkills || [{ name: "", level: "required", yearsRequired: 0 }],
      qualifications: initialData?.qualifications || [""],
      responsibilities: initialData?.responsibilities || [""],
      salaryRange: initialData?.salaryRange || { min: 0, max: 0, currency: "USD", period: "yearly" },
      status: initialData?.status || "draft",
      openings: initialData?.openings || 1,
      applicationDeadline: initialData?.applicationDeadline ? new Date(initialData.applicationDeadline) : undefined,
      startDate: initialData?.startDate ? new Date(initialData.startDate) : undefined,
      organization: initialData?.organization?.toString() || user?.organization || "",
    },
  })

  const { fields: skillFields, append: appendSkill, remove: removeSkill } = useFieldArray({
    control,
    name: "requiredSkills"
  })

  const { fields: qualificationFields, append: appendQualification, remove: removeQualification } = useFieldArray({
    control,
    name: "qualifications"
  })

  const { fields: responsibilityFields, append: appendResponsibility, remove: removeResponsibility } = useFieldArray({
    control,
    name: "responsibilities"
  })

  const handleFormSubmit = async (data: JobFormData) => {
    try {
      // Filter out empty strings from arrays
      const cleanedData = {
        ...data,
        requiredSkills: data.requiredSkills.filter(skill => skill.name.trim() !== ''),
        qualifications: data.qualifications.filter(qual => qual.trim() !== ''),
        responsibilities: data.responsibilities.filter(resp => resp.trim() !== ''),
        salaryRange: includeSalary ? data.salaryRange : undefined,
      }

      await onSubmit(cleanedData as JobFormData)

      toast({
        title: mode === 'create' ? "Job Created" : "Job Updated",
        description: mode === 'create'
          ? "The job posting has been created successfully."
          : "The job posting has been updated successfully.",
      })

      if (mode === 'create') {
        reset()
      }
    } catch (error) {
      // Get more specific error message
      let errorMessage = mode === 'create'
        ? "Failed to create job posting. Please try again."
        : "Failed to update job posting. Please try again."

      if (error instanceof Error) {
        errorMessage = error.message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Job Title *</Label>
              <Input
                id="title"
                {...register("title")}
                placeholder="e.g. Senior Frontend Developer"
                className={errors.title ? "border-red-500" : ""}
              />
              {errors.title && (
                <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="department">Department *</Label>
              <Input
                id="department"
                {...register("department")}
                placeholder="e.g. Engineering"
                className={errors.department ? "border-red-500" : ""}
              />
              {errors.department && (
                <p className="text-sm text-red-500 mt-1">{errors.department.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="location">Location *</Label>
              <Input
                id="location"
                {...register("location")}
                placeholder="e.g. San Francisco, CA"
                className={errors.location ? "border-red-500" : ""}
              />
              {errors.location && (
                <p className="text-sm text-red-500 mt-1">{errors.location.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="organization">Organization *</Label>
              <Select 
                value={watch("organization")} 
                onValueChange={(value) => setValue("organization", value)}
              >
                <SelectTrigger className={errors.organization ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select organization" />
                </SelectTrigger>
                <SelectContent>
                  {organizations?.map((org) => (
                    <SelectItem key={org._id} value={org._id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.organization && (
                <p className="text-sm text-red-500 mt-1">{errors.organization.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="description">Job Description *</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Provide a detailed description of the job role, expectations, and what makes this opportunity exciting..."
              rows={6}
              className={errors.description ? "border-red-500" : ""}
            />
            {errors.description && (
              <p className="text-sm text-red-500 mt-1">{errors.description.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Job Details */}
      <Card>
        <CardHeader>
          <CardTitle>Job Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="jobType">Job Type *</Label>
              <Select
                value={watch("jobType")}
                onValueChange={(value) => setValue("jobType", value as JobType)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select job type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full_time">Full Time</SelectItem>
                  <SelectItem value="part_time">Part Time</SelectItem>
                  <SelectItem value="contract">Contract</SelectItem>
                  <SelectItem value="internship">Internship</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="workLocation">Work Location *</Label>
              <Select
                value={watch("workLocation")}
                onValueChange={(value) => setValue("workLocation", value as WorkLocation)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select work location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="remote">Remote</SelectItem>
                  <SelectItem value="onsite">On-site</SelectItem>
                  <SelectItem value="hybrid">Hybrid</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="experienceLevel">Experience Level *</Label>
              <Select
                value={watch("experienceLevel")}
                onValueChange={(value) => setValue("experienceLevel", value as ExperienceLevel)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select experience level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="entry">Entry Level</SelectItem>
                  <SelectItem value="mid">Mid Level</SelectItem>
                  <SelectItem value="senior">Senior Level</SelectItem>
                  <SelectItem value="lead">Lead</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="openings">Number of Openings *</Label>
              <Input
                id="openings"
                type="number"
                min="1"
                {...register("openings", { valueAsNumber: true })}
                className={errors.openings ? "border-red-500" : ""}
              />
              {errors.openings && (
                <p className="text-sm text-red-500 mt-1">{errors.openings.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status *</Label>
              <Select
                value={watch("status")}
                onValueChange={(value) => setValue("status", value as JobStatus)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Required Skills */}
      <Card>
        <CardHeader>
          <CardTitle>Required Skills</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {skillFields.map((field, index) => (
            <div key={field.id} className="flex items-end space-x-2">
              <div className="flex-1">
                <Label htmlFor={`skill-${index}`}>Skill Name</Label>
                <Input
                  id={`skill-${index}`}
                  {...register(`requiredSkills.${index}.name`)}
                  placeholder="e.g. React, TypeScript, Python"
                  className={errors.requiredSkills?.[index]?.name ? "border-red-500" : ""}
                />
                {errors.requiredSkills?.[index]?.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.requiredSkills[index]?.name?.message}</p>
                )}
              </div>
              <div className="w-32">
                <Label htmlFor={`skill-level-${index}`}>Level</Label>
                <Select
                  value={watch(`requiredSkills.${index}.level`)}
                  onValueChange={(value) => setValue(`requiredSkills.${index}.level`, value as "required" | "preferred" | "nice_to_have")}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="required">Required</SelectItem>
                    <SelectItem value="preferred">Preferred</SelectItem>
                    <SelectItem value="nice_to_have">Nice to Have</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-24">
                <Label htmlFor={`skill-years-${index}`}>Years</Label>
                <Input
                  id={`skill-years-${index}`}
                  type="number"
                  min="0"
                  {...register(`requiredSkills.${index}.yearsRequired`, { valueAsNumber: true })}
                  placeholder="0"
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeSkill(index)}
                disabled={skillFields.length === 1}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button
            type="button"
            variant="outline"
            onClick={() => appendSkill({ name: "", level: "required", yearsRequired: 0 })}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Skill
          </Button>
          {errors.requiredSkills && (
            <p className="text-sm text-red-500">{errors.requiredSkills.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Qualifications */}
      <Card>
        <CardHeader>
          <CardTitle>Qualifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {qualificationFields.map((field, index) => (
            <div key={field.id} className="flex items-center space-x-2">
              <div className="flex-1">
                <Input
                  {...register(`qualifications.${index}`)}
                  placeholder="e.g. Bachelor's degree in Computer Science or related field"
                  className={errors.qualifications?.[index] ? "border-red-500" : ""}
                />
                {errors.qualifications?.[index] && (
                  <p className="text-sm text-red-500 mt-1">{errors.qualifications[index]?.message}</p>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeQualification(index)}
                disabled={qualificationFields.length === 1}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button
            type="button"
            variant="outline"
            onClick={() => appendQualification("")}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Qualification
          </Button>
          {errors.qualifications && (
            <p className="text-sm text-red-500">{errors.qualifications.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Responsibilities */}
      <Card>
        <CardHeader>
          <CardTitle>Responsibilities</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {responsibilityFields.map((field, index) => (
            <div key={field.id} className="flex items-center space-x-2">
              <div className="flex-1">
                <Input
                  {...register(`responsibilities.${index}`)}
                  placeholder="e.g. Develop and maintain frontend applications using React"
                  className={errors.responsibilities?.[index] ? "border-red-500" : ""}
                />
                {errors.responsibilities?.[index] && (
                  <p className="text-sm text-red-500 mt-1">{errors.responsibilities[index]?.message}</p>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => removeResponsibility(index)}
                disabled={responsibilityFields.length === 1}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button
            type="button"
            variant="outline"
            onClick={() => appendResponsibility("")}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Responsibility
          </Button>
          {errors.responsibilities && (
            <p className="text-sm text-red-500">{errors.responsibilities.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Salary Range (Optional) */}
      <Card>
        <CardHeader>
          <CardTitle>Salary Range (Optional)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="includeSalary"
              checked={includeSalary}
              onChange={(e) => setIncludeSalary(e.target.checked)}
              className="rounded"
            />
            <Label htmlFor="includeSalary">Include salary range in job posting</Label>
          </div>

          {includeSalary && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="salaryMin">Minimum</Label>
                <Input
                  id="salaryMin"
                  type="number"
                  min="0"
                  {...register("salaryRange.min", { valueAsNumber: true })}
                  placeholder="50000"
                  className={errors.salaryRange?.min ? "border-red-500" : ""}
                />
                {errors.salaryRange?.min && (
                  <p className="text-sm text-red-500 mt-1">{errors.salaryRange.min.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="salaryMax">Maximum</Label>
                <Input
                  id="salaryMax"
                  type="number"
                  min="0"
                  {...register("salaryRange.max", { valueAsNumber: true })}
                  placeholder="80000"
                  className={errors.salaryRange?.max ? "border-red-500" : ""}
                />
                {errors.salaryRange?.max && (
                  <p className="text-sm text-red-500 mt-1">{errors.salaryRange.max.message}</p>
                )}
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={watch("salaryRange.currency")}
                  onValueChange={(value) => setValue("salaryRange.currency", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="period">Period</Label>
                <Select
                  value={watch("salaryRange.period")}
                  onValueChange={(value) => setValue("salaryRange.period", value as "hourly" | "monthly" | "yearly")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dates */}
      <Card>
        <CardHeader>
          <CardTitle>Important Dates</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="applicationDeadline">Application Deadline</Label>
              <Input
                id="applicationDeadline"
                type="date"
                {...register("applicationDeadline", { valueAsDate: true })}
              />
            </div>
            <div>
              <Label htmlFor="startDate">Expected Start Date</Label>
              <Input
                id="startDate"
                type="date"
                {...register("startDate", { valueAsDate: true })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting || isLoading}>
          {isSubmitting || isLoading ? "Saving..." : mode === 'create' ? "Create Job" : "Update Job"}
        </Button>
      </div>
    </form>
  )
}
