import { useState, useEffect, useCallback } from 'react'
import { SearchFilter } from './SearchFilter'
import type { SearchField, FilterField } from '@/types/search'
import { useSearchOrganizations, type IOrganization } from '@/hooks/useOrganizations'

interface OrganizationSearchFilterProps {
  onResultsChange: (organizations: IOrganization[] | null) => void
  className?: string
}

export function OrganizationSearchFilter({ onResultsChange, className }: OrganizationSearchFilterProps) {
  const [searchParams, setSearchParams] = useState<{ name?: string; domain?: string; address?: string }>({})
  
  // Search query
  const { data: searchResults } = useSearchOrganizations(searchParams)

  // Update results when search changes
  useEffect(() => {
    const hasSearch = Object.values(searchParams).some(value => value)

    if (hasSearch) {
      onResultsChange(searchResults || null)
    } else {
      onResultsChange(null) // Show all organizations
    }
  }, [searchResults, searchParams])

  const searchFields: SearchField[] = [
    {
      key: 'name',
      label: 'Organization Name',
      placeholder: 'Search by organization name...',
      type: 'text'
    },
    {
      key: 'domain',
      label: 'Domain',
      placeholder: 'Search by domain...',
      type: 'text'
    },
    {
      key: 'address',
      label: 'Address',
      placeholder: 'Search by address...',
      type: 'text'
    }
  ]

  // Organizations don't have complex filters in the current API
  const filterFields: FilterField[] = []

  const handleSearchChange = useCallback((params: Record<string, string>) => {
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '')
    )
    setSearchParams(filteredParams)
  }, [])

  const handleFilterChange = useCallback((params: Record<string, string | string[]>) => {
    // No filters for organizations currently
  }, [])

  const handleClear = useCallback(() => {
    setSearchParams({})
  }, [])

  return (
    <SearchFilter
      searchFields={searchFields}
      filterFields={filterFields}
      onSearchChange={handleSearchChange}
      onFilterChange={handleFilterChange}
      onClear={handleClear}
      className={className}
    />
  )
}
