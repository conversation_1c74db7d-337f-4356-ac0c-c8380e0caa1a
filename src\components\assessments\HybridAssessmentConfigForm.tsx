import React from 'react'
import { UseFormReturn } from 'react-hook-form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Zap, ArrowRight, Brain, FileText, Shuffle, Scale } from 'lucide-react'
import { AIInterviewConfigForm } from './AIInterviewConfigForm'
import { ManualAssessmentConfigForm } from './ManualAssessmentConfigForm'
import type { AssessmentTypesResponse, SequenceType } from '@/types'

interface HybridAssessmentConfigFormProps {
  form: UseFormReturn<any>
  assessmentTypes?: AssessmentTypesResponse
}

export function HybridAssessmentConfigForm({ form, assessmentTypes }: HybridAssessmentConfigFormProps) {
  const { register, watch, setValue, formState: { errors } } = form

  const sequence = watch('hybridAssessmentConfig.sequence') || 'ai-first'
  const combinedCriteria = watch('hybridAssessmentConfig.combinedPassingCriteria')

  const getSequenceIcon = (seq: SequenceType) => {
    switch (seq) {
      case 'ai-first':
        return <div className="flex items-center space-x-1">
          <Brain className="h-4 w-4" />
          <ArrowRight className="h-3 w-3" />
          <FileText className="h-4 w-4" />
        </div>
      case 'manual-first':
        return <div className="flex items-center space-x-1">
          <FileText className="h-4 w-4" />
          <ArrowRight className="h-3 w-3" />
          <Brain className="h-4 w-4" />
        </div>
      case 'parallel':
        return <div className="flex items-center space-x-1">
          <Brain className="h-4 w-4" />
          <Shuffle className="h-3 w-3" />
          <FileText className="h-4 w-4" />
        </div>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Sequence Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-purple-600" />
            <span>Hybrid Assessment Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="sequence">Assessment Sequence</Label>
            <Select
              value={sequence}
              onValueChange={(value: SequenceType) => setValue('hybridAssessmentConfig.sequence', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select sequence" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ai-first">
                  <div className="flex items-center space-x-2">
                    {getSequenceIcon('ai-first')}
                    <span>AI Interview First</span>
                  </div>
                </SelectItem>
                <SelectItem value="manual-first">
                  <div className="flex items-center space-x-2">
                    {getSequenceIcon('manual-first')}
                    <span>Manual Assessment First</span>
                  </div>
                </SelectItem>
                <SelectItem value="parallel">
                  <div className="flex items-center space-x-2">
                    {getSequenceIcon('parallel')}
                    <span>Parallel (Candidate Choice)</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.hybridAssessmentConfig?.sequence && (
              <p className="text-sm text-red-600 mt-1">
                {errors.hybridAssessmentConfig.sequence.message}
              </p>
            )}
          </div>

          {/* Sequence Description */}
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              {getSequenceIcon(sequence)}
              <Badge variant="outline" className="text-purple-700 border-purple-300">
                {sequence.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Badge>
            </div>
            <p className="text-sm text-purple-800">
              {sequence === 'ai-first' && 'Candidates complete the AI interview first, then proceed to the manual assessment.'}
              {sequence === 'manual-first' && 'Candidates complete the manual assessment first, then proceed to the AI interview.'}
              {sequence === 'parallel' && 'Candidates can choose the order or complete both assessments simultaneously.'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Combined Passing Criteria */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Scale className="h-5 w-5 text-orange-600" />
            <span>Combined Scoring (Optional)</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Enable Combined Scoring</Label>
              <p className="text-sm text-gray-500">
                Set weighted criteria for both assessments
              </p>
            </div>
            <Switch
              checked={!!combinedCriteria}
              onCheckedChange={(checked) => {
                if (checked) {
                  setValue('hybridAssessmentConfig.combinedPassingCriteria', {
                    aiWeight: 50,
                    manualWeight: 50,
                    minimumCombinedScore: 70
                  })
                } else {
                  setValue('hybridAssessmentConfig.combinedPassingCriteria', undefined)
                }
              }}
            />
          </div>

          {combinedCriteria && (
            <div className="space-y-4 p-4 border rounded-lg bg-orange-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="ai-weight">AI Interview Weight (%)</Label>
                  <Input
                    id="ai-weight"
                    type="number"
                    min="0"
                    max="100"
                    value={combinedCriteria.aiWeight || 50}
                    onChange={(e) => setValue('hybridAssessmentConfig.combinedPassingCriteria.aiWeight', 
                      parseInt(e.target.value) || 0)}
                  />
                </div>

                <div>
                  <Label htmlFor="manual-weight">Manual Assessment Weight (%)</Label>
                  <Input
                    id="manual-weight"
                    type="number"
                    min="0"
                    max="100"
                    value={combinedCriteria.manualWeight || 50}
                    onChange={(e) => setValue('hybridAssessmentConfig.combinedPassingCriteria.manualWeight', 
                      parseInt(e.target.value) || 0)}
                  />
                </div>

                <div>
                  <Label htmlFor="minimum-score">Minimum Combined Score (%)</Label>
                  <Input
                    id="minimum-score"
                    type="number"
                    min="0"
                    max="100"
                    value={combinedCriteria.minimumCombinedScore || 70}
                    onChange={(e) => setValue('hybridAssessmentConfig.combinedPassingCriteria.minimumCombinedScore', 
                      parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              {/* Weight Validation */}
              {combinedCriteria.aiWeight + combinedCriteria.manualWeight !== 100 && (
                <div className="bg-yellow-100 border border-yellow-300 p-3 rounded">
                  <p className="text-sm text-yellow-800">
                    ⚠️ Weights should add up to 100%. Current total: {combinedCriteria.aiWeight + combinedCriteria.manualWeight}%
                  </p>
                </div>
              )}

              {/* Scoring Preview */}
              <div className="bg-white p-3 rounded border">
                <h5 className="font-medium mb-2">Scoring Formula</h5>
                <p className="text-sm text-gray-600">
                  Combined Score = (AI Score × {combinedCriteria.aiWeight}%) + (Manual Score × {combinedCriteria.manualWeight}%)
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Pass Threshold: {combinedCriteria.minimumCombinedScore}%
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* AI Interview Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Brain className="h-5 w-5 text-blue-600" />
          <span>AI Interview Component</span>
        </h3>
        
        {/* Create a nested form context for AI Interview */}
        <div className="pl-4 border-l-2 border-blue-200">
          <AIInterviewConfigForm 
            form={{
              ...form,
              register: (name, options) => form.register(`hybridAssessmentConfig.aiInterviewConfig.${name.replace('aiInterviewConfig.', '')}`, options),
              watch: (name) => form.watch(`hybridAssessmentConfig.aiInterviewConfig.${name?.replace('aiInterviewConfig.', '') || ''}`),
              setValue: (name, value, options) => form.setValue(`hybridAssessmentConfig.aiInterviewConfig.${name.replace('aiInterviewConfig.', '')}`, value, options),
              formState: {
                ...form.formState,
                errors: form.formState.errors.hybridAssessmentConfig?.aiInterviewConfig || {}
              }
            } as any}
            assessmentTypes={assessmentTypes}
          />
        </div>
      </div>

      <Separator />

      {/* Manual Assessment Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <FileText className="h-5 w-5 text-green-600" />
          <span>Manual Assessment Component</span>
        </h3>
        
        {/* Create a nested form context for Manual Assessment */}
        <div className="pl-4 border-l-2 border-green-200">
          <ManualAssessmentConfigForm 
            form={{
              ...form,
              register: (name, options) => form.register(`hybridAssessmentConfig.manualAssessmentConfig.${name.replace('manualAssessmentConfig.', '')}`, options),
              watch: (name) => form.watch(`hybridAssessmentConfig.manualAssessmentConfig.${name?.replace('manualAssessmentConfig.', '') || ''}`),
              setValue: (name, value, options) => form.setValue(`hybridAssessmentConfig.manualAssessmentConfig.${name.replace('manualAssessmentConfig.', '')}`, value, options),
              formState: {
                ...form.formState,
                errors: form.formState.errors.hybridAssessmentConfig?.manualAssessmentConfig || {}
              }
            } as any}
            assessmentTypes={assessmentTypes}
          />
        </div>
      </div>
    </div>
  )
}
