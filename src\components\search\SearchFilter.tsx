import { useState, useEffect, useCallback } from 'react'
import { Search, Filter, X, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import type { SearchField, FilterField } from '@/types/search'

interface SearchFilterProps {
  searchFields: SearchField[]
  filterFields: FilterField[]
  onSearchChange: (searchParams: Record<string, string>) => void
  onFilterChange: (filterParams: Record<string, string | string[]>) => void
  onClear: () => void
  className?: string
}

export function SearchFilter({
  searchFields,
  filterFields,
  onSearchChange,
  onFilterChange,
  onClear,
  className = ''
}: SearchFilterProps) {
  const [searchParams, setSearchParams] = useState<Record<string, string>>({})
  const [filterParams, setFilterParams] = useState<Record<string, string | string[]>>({})
  const [isFilterOpen, setIsFilterOpen] = useState(false)

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearchChange(searchParams)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchParams, onSearchChange])

  // Filter change handler
  useEffect(() => {
    onFilterChange(filterParams)
  }, [filterParams])

  const handleSearchChange = useCallback((key: string, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const handleFilterChange = useCallback((key: string, value: string | string[]) => {
    setFilterParams(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const handleClear = useCallback(() => {
    setSearchParams({})
    setFilterParams({})
    onClear()
  }, [onClear])

  const getActiveFiltersCount = () => {
    return Object.values(filterParams).filter(value => 
      Array.isArray(value) ? value.length > 0 : value !== ''
    ).length
  }

  const getActiveSearchCount = () => {
    return Object.values(searchParams).filter(value => value !== '').length
  }

  const hasActiveFilters = getActiveFiltersCount() > 0 || getActiveSearchCount() > 0

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        {searchFields.map((field) => (
          <div key={field.key} className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              {field.type === 'text' ? (
                <Input
                  placeholder={field.placeholder}
                  value={searchParams[field.key] || ''}
                  onChange={(e) => handleSearchChange(field.key, e.target.value)}
                  className="pl-10"
                />
              ) : (
                <Select
                  value={searchParams[field.key] || ''}
                  onValueChange={(value) => handleSearchChange(field.key, value)}
                >
                  <SelectTrigger className="pl-10">
                    <SelectValue placeholder={field.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All {field.label}</SelectItem>
                    {field.options?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        ))}

        {/* Filter Button */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="relative"
            onClick={() => setIsFilterOpen(!isFilterOpen)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                {getActiveFiltersCount()}
              </Badge>
            )}
            <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${isFilterOpen ? 'rotate-180' : ''}`} />
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={handleClear}>
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {isFilterOpen && (
        <div className="bg-white border rounded-lg p-4 shadow-sm">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Filters</h4>
              {hasActiveFilters && (
                <Button variant="ghost" size="sm" onClick={handleClear}>
                  Clear all
                </Button>
              )}
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filterFields.map((field) => (
                <div key={field.key} className="space-y-2">
                  <Label className="text-sm font-medium">{field.label}</Label>
                  <Select
                    value={filterParams[field.key] as string || ''}
                    onValueChange={(value) => handleFilterChange(field.key, value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All {field.label}</SelectItem>
                      {field.options.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(searchParams).map(([key, value]) => {
            if (!value) return null
            const field = searchFields.find(f => f.key === key)
            return (
              <Badge key={key} variant="secondary" className="flex items-center gap-1">
                {field?.label}: {value}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleSearchChange(key, '')}
                />
              </Badge>
            )
          })}
          
          {Object.entries(filterParams).map(([key, value]) => {
            if (!value || (Array.isArray(value) && value.length === 0)) return null
            const field = filterFields.find(f => f.key === key)
            const displayValue = Array.isArray(value) ? value.join(', ') : value
            const option = field?.options.find(opt => opt.value === value)
            
            return (
              <Badge key={key} variant="secondary" className="flex items-center gap-1">
                {field?.label}: {option?.label || displayValue}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleFilterChange(key, Array.isArray(value) ? [] : '')}
                />
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}
