import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'

// Temporary type definitions until import issues are fixed
interface IOrganization {
  _id: string;
  name: string;
  address?: string;
  domain: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  address?: string;
}

export function useOrganizations() {
  return useQuery({
    queryKey: ['organizations'],
    queryFn: () => api.get<IOrganization[]>('/organization'),
  })
}

export function useCreateOrganization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: OrganizationFormData) =>
      api.post<IOrganization>('/organization', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizations'] })
    },
  })
}

export function useUpdateOrganization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: OrganizationFormData }) =>
      api.patch<IOrganization>(`/organization/update/${id}`, data), // Note: API uses PATCH and /update/ prefix
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizations'] })
    },
  })
}

export function useDeleteOrganization() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => api.delete(`/organization/delete/${id}`), // Note: API uses /delete/ prefix
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizations'] })
    },
  })
}

// Search organizations by name, domain, or address
export function useSearchOrganizations(params: { name?: string; domain?: string; address?: string }) {
  return useQuery({
    queryKey: ['organizations', 'search', params],
    queryFn: () => api.get<IOrganization[]>('/organization/search', params),
    enabled: !!(params.name || params.domain || params.address),
  })
}

// Get organization analytics
export function useOrganizationAnalytics() {
  return useQuery({
    queryKey: ['organizations', 'analytics'],
    queryFn: () => api.get<{
      totalOrganizations: number;
    }>('/organization/analytics'),
  })
}