import { useState } from 'react'
import { Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DataTable } from '@/components/tables/DataTable'
import { OrganizationForm } from '@/components/forms/OrganizationForm'
import { OrganizationDetail } from './OrganizationDetail'
import { OrganizationSearchFilter } from '@/components/search/OrganizationSearchFilter'
import { useOrganizations, useCreateOrganization, useUpdateOrganization, useDeleteOrganization } from '@/hooks/useOrganizations'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

// Temporary type definitions until import issues are fixed
interface IOrganization {
  _id: string;
  name: string;
  address?: string;
  domain: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface OrganizationFormData {
  name: string;
  domain: string;
  address?: string;
}

export function OrganizationList() {
  const { data: organizations, isLoading } = useOrganizations()
  const createMutation = useCreateOrganization()
  const updateMutation = useUpdateOrganization()
  const deleteMutation = useDeleteOrganization()
  const { toast } = useToast()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingOrganization, setEditingOrganization] = useState<IOrganization | null>(null)
  const [viewingOrganizationId, setViewingOrganizationId] = useState<string | null>(null)
  const [filteredOrganizations, setFilteredOrganizations] = useState<IOrganization[] | null>(null)

  // Use filtered organizations if available, otherwise use all organizations
  const displayOrganizations = filteredOrganizations || organizations || []

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'domain', label: 'Domain', sortable: true },
    {
      key: 'address',
      label: 'Address',
      sortable: false,
      render: (value: string) => value || 'Not provided'
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value: string) => formatDate(value)
    },
  ]

  const handleCreate = async (data: OrganizationFormData) => {
    await createMutation.mutateAsync(data)
    setIsCreateDialogOpen(false)
  }

  const handleUpdate = async (data: OrganizationFormData) => {
    if (editingOrganization) {
      await updateMutation.mutateAsync({
        id: editingOrganization._id.toString(),
        data,
      })
      setEditingOrganization(null)
    }
  }

  const handleDelete = async (id: string) => {
    const organization = displayOrganizations.find(org => org._id.toString() === id)
    const orgName = organization?.name || 'this organization'

    if (!confirm(`Are you sure you want to delete ${orgName}? This action cannot be undone.`)) {
      return
    }

    try {
      await deleteMutation.mutateAsync(id)
      toast({
        title: "Organization Deleted",
        description: `${orgName} has been deleted successfully.`,
      })
    } catch (error: any) {
      console.error('Delete organization error:', error)

      toast({
        title: "Delete Failed",
        description: error?.message || "Failed to delete organization.",
        variant: "destructive",
      })
    }
  }

  const handleEdit = (id: string) => {
    const org = displayOrganizations.find(o => o._id.toString() === id)
    if (org) {
      setEditingOrganization(org)
    }
  }

  const handleView = (id: string) => {
    setViewingOrganizationId(id)
  }

  // Show detail view if viewing an organization
  if (viewingOrganizationId) {
    return (
      <OrganizationDetail
        organizationId={viewingOrganizationId}
        onBack={() => setViewingOrganizationId(null)}
        onDelete={() => setViewingOrganizationId(null)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter with Actions */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex-1">
            <OrganizationSearchFilter
              onResultsChange={setFilteredOrganizations}
              className="border-0 p-0 shadow-none"
            />
          </div>
          <div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Organization
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Organization</DialogTitle>
                </DialogHeader>
                <OrganizationForm
                  mode="create"
                  onSubmit={handleCreate}
                  onCancel={() => setIsCreateDialogOpen(false)}
                  isLoading={createMutation.isPending}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      <DataTable
        data={displayOrganizations}
        columns={columns}
        loading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
      />

      {/* Edit Dialog */}
      <Dialog open={!!editingOrganization} onOpenChange={() => setEditingOrganization(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
          </DialogHeader>
          {editingOrganization && (
            <OrganizationForm
              mode="edit"
              initialData={editingOrganization}
              onSubmit={handleUpdate}
              onCancel={() => setEditingOrganization(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}