import type { IScreeningQuestion, IJobConfig } from '@/types'

/**
 * Retrieves screening questions from a job configuration
 */
export function getScreeningQuestionsFromJobConfig(jobConfig: IJobConfig | null): IScreeningQuestion[] {
  if (!jobConfig?.stageConfig) return []

  const screeningStage = jobConfig.stageConfig.find(stage => stage.stage === 'screening')
  if (!screeningStage?.action?.params?.screeningConfig?.questions) return []

  return screeningStage.action.params.screeningConfig.questions
}

/**
 * Formats screening questions for the API call
 * Converts the questions array to a JSON string as expected by the API
 */
export function formatQuestionsForAPI(questions: IScreeningQuestion[]): string {
  if (!questions || questions.length === 0) {
    return JSON.stringify([])
  }

  // Filter out incomplete questions and format for API
  const validQuestions = questions.filter(q => q.question.trim() !== '')
  
  // Format questions for the screening call API
  const formattedQuestions = validQuestions.map(q => ({
    id: q.id,
    question: q.question,
    type: q.type || 'yes-no',
    options: q.options || [],
    correctAnswer: q.correctAnswer,
    required: q.required || false
  }))

  return JSON.stringify(formattedQuestions)
}

/**
 * Extracts company name and role from job configuration
 */
export function getJobDetailsFromConfig(jobConfig: IJobConfig | null): { companyName: string; role: string } {
  // This would typically come from the job data associated with the config
  // For now, return defaults - this should be enhanced based on your job data structure
  return {
    companyName: 'PlacedHQ',
    role: 'Software Developer'
  }
}

/**
 * Validates screening questions before API call
 */
export function validateScreeningQuestions(questions: IScreeningQuestion[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!questions || questions.length === 0) {
    errors.push('At least one screening question is required')
    return { isValid: false, errors }
  }

  questions.forEach((question, index) => {
    if (!question.question.trim()) {
      errors.push(`Question ${index + 1}: Question text is required`)
    }

    if (question.type === 'multiple-choice' && (!question.options || question.options.length < 2)) {
      errors.push(`Question ${index + 1}: Multiple choice questions must have at least 2 options`)
    }

    if ((question.type === 'multiple-choice' || question.type === 'yes-no') && !question.correctAnswer) {
      errors.push(`Question ${index + 1}: Correct answer is required for scoring`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Gets default screening questions for a new configuration
 */
export function getDefaultScreeningQuestions(): IScreeningQuestion[] {
  return [
    {
      id: 1,
      question: 'Are you comfortable with remote work?',
      type: 'yes-no',
      options: ['Yes', 'No'],
      correctAnswer: 'Yes',
      required: true
    },
    {
      id: 2,
      question: 'Do you have experience with the required technologies?',
      type: 'yes-no',
      options: ['Yes', 'No'],
      correctAnswer: 'Yes',
      required: true
    }
  ]
}
