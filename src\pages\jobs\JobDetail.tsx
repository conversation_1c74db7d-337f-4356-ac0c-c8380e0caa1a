import { useState } from 'react'
import { ArrowLeft, Edit, Trash2, Briefcase, MapPin, Clock, DollarSign, Users, Calendar, Building, Settings, Play, Square } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { JobForm } from '@/components/forms/JobForm'
import { useUpdateJob, useDeleteJob, type IJob, type JobFormData } from '@/hooks/useJobs'
import { formatDate } from '@/lib/utils'

interface JobDetailProps {
  job: IJob;
  onBack: () => void;
  onEdit: (job: IJob) => void;
  onDelete: (id: string) => void;
}

export function JobDetail({ job, onBack, onEdit, onDelete }: JobDetailProps) {
  const updateMutation = useUpdateJob()
  const deleteMutation = useDeleteJob()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  const handleUpdate = async (data: JobFormData) => {
    await updateMutation.mutateAsync({ id: job._id, data })
    setIsEditDialogOpen(false)
  }

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this job posting?')) {
      await onDelete(job._id)
      onBack()
    }
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getJobTypeColor = (jobType: string) => {
    switch (jobType) {
      case 'full_time': return 'bg-blue-100 text-blue-800'
      case 'part_time': return 'bg-purple-100 text-purple-800'
      case 'contract': return 'bg-orange-100 text-orange-800'
      case 'internship': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }



  const formatJobType = (jobType: string) => {
    return jobType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const formatSalary = (salaryRange?: { min: number; max: number; currency: string; period: string }) => {
    if (!salaryRange) return 'Not specified'
    const { min, max, currency, period } = salaryRange
    return `${currency} ${min.toLocaleString()} - ${max.toLocaleString()} / ${period}`
  }

  const formatSkillLevel = (level: string) => {
    switch (level) {
      case 'required': return 'Required'
      case 'preferred': return 'Preferred'
      case 'nice_to_have': return 'Nice to Have'
      default: return level
    }
  }

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'required': return 'bg-red-100 text-red-800'
      case 'preferred': return 'bg-yellow-100 text-yellow-800'
      case 'nice_to_have': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Jobs
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{job.title}</h1>
            <p className="text-gray-600">{job.department} • {job.location}</p>
          </div>
        </div>
        <div className="flex space-x-2">

          <Button variant="outline" onClick={() => setIsEditDialogOpen(true)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Status and Key Info */}
      <div className="flex flex-wrap items-center gap-4">
        <Badge className={getStatusColor(job.status)}>
          {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
        </Badge>
        <Badge className={getJobTypeColor(job.jobType)}>
          {formatJobType(job.jobType)}
        </Badge>
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <MapPin className="h-4 w-4" />
          <span className="capitalize">{job.workLocation}</span>
        </div>
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <Users className="h-4 w-4" />
          <span>{job.openings} opening{job.openings !== 1 ? 's' : ''}</span>
        </div>
        <div className="flex items-center space-x-1 text-sm text-gray-600">
          <Clock className="h-4 w-4" />
          <span>Posted {formatDate(job.postedDate)}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Job Description */}
          <Card>
            <CardHeader>
              <CardTitle>Job Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="whitespace-pre-wrap">{job.description}</p>
              </div>
            </CardContent>
          </Card>



          {/* Required Skills */}
          <Card>
            <CardHeader>
              <CardTitle>Required Skills</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {job.requiredSkills.map((skill, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="font-medium">{skill.name}</span>
                      {skill.yearsRequired && skill.yearsRequired > 0 && (
                        <span className="text-sm text-gray-600 ml-2">
                          ({skill.yearsRequired} year{skill.yearsRequired !== 1 ? 's' : ''})
                        </span>
                      )}
                    </div>
                    <Badge className={getSkillLevelColor(skill.level)}>
                      {formatSkillLevel(skill.level)}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Qualifications */}
          <Card>
            <CardHeader>
              <CardTitle>Qualifications</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {job.qualifications.map((qualification, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-1">•</span>
                    <span>{qualification}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Responsibilities */}
          <Card>
            <CardHeader>
              <CardTitle>Key Responsibilities</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {job.responsibilities.map((responsibility, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>{responsibility}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Job Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Job Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Experience Level</label>
                <p className="text-gray-900 capitalize">{job.experienceLevel.replace('_', ' ')}</p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-gray-500">Department</label>
                <div className="flex items-center space-x-2">
                  <Building className="h-4 w-4 text-gray-400" />
                  <p className="text-gray-900">{job.department}</p>
                </div>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-gray-500">Location</label>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <p className="text-gray-900">{job.location}</p>
                </div>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-gray-500">Salary Range</label>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <p className="text-gray-900">{formatSalary(job.salaryRange)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Important Dates */}
          <Card>
            <CardHeader>
              <CardTitle>Important Dates</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Posted Date</label>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <p className="text-gray-900">{formatDate(job.postedDate)}</p>
                </div>
              </div>
              {job.applicationDeadline && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Application Deadline</label>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <p className="text-gray-900">{formatDate(job.applicationDeadline)}</p>
                    </div>
                  </div>
                </>
              )}
              {job.startDate && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-gray-500">Expected Start Date</label>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <p className="text-gray-900">{formatDate(job.startDate)}</p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">0</div>
                <div className="text-sm text-gray-500">Applications</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">0</div>
                <div className="text-sm text-gray-500">Interviews</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">0</div>
                <div className="text-sm text-gray-500">Offers</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Job</DialogTitle>
          </DialogHeader>
          <JobForm
            mode="edit"
            initialData={job}
            onSubmit={handleUpdate}
            onCancel={() => setIsEditDialogOpen(false)}
            isLoading={updateMutation.isPending}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
