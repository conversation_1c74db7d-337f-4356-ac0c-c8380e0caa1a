import { useState } from 'react'
import { Trash2, <PERSON>, Eye, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { 
  useJobConfigs, 
  useJobConfig, 
  useDeleteJobConfig, 
  useUpdateJobConfigStage 
} from '@/hooks/useJobConfig'
import type { IStageScheduling, INextStatusConfig } from '@/types'

interface JobConfigManagerProps {
  selectedJobId?: string
}

export function JobConfigManager({ selectedJobId }: JobConfigManagerProps) {
  const { toast } = useToast()
  const { data: jobConfigs, isLoading: isLoadingConfigs } = useJobConfigs()
  const { data: selectedJobConfig } = useJobConfig(selectedJobId || '')
  const deleteJobConfigMutation = useDeleteJobConfig()
  const updateStageMutation = useUpdateJobConfigStage()

  const [editingStage, setEditingStage] = useState<{
    jobId: string
    stage: string
    scheduling?: IStageScheduling
    next?: INextStatusConfig[]
  } | null>(null)

  const handleDeleteJobConfig = async (jobId: string) => {
    try {
      await deleteJobConfigMutation.mutateAsync(jobId)
      toast({
        title: "Job Configuration Deleted",
        description: `Configuration for job ${jobId} has been deleted.`,
      })
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete job configuration.",
        variant: "destructive",
      })
    }
  }

  const handleUpdateStage = async () => {
    if (!editingStage) return

    try {
      await updateStageMutation.mutateAsync({
        jobId: editingStage.jobId,
        stage: editingStage.stage,
        data: {
          scheduling: editingStage.scheduling,
          next: editingStage.next,
        }
      })

      toast({
        title: "Stage Updated",
        description: `Stage ${editingStage.stage} has been updated.`,
      })
      setEditingStage(null)
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update stage configuration.",
        variant: "destructive",
      })
    }
  }

  if (isLoadingConfigs) {
    return <div className="p-4">Loading job configurations...</div>
  }

  return (
    <div className="space-y-6">
      {/* Job Configurations List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Job Configurations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!jobConfigs || jobConfigs.length === 0 ? (
            <p className="text-muted-foreground">No job configurations found.</p>
          ) : (
            <div className="space-y-4">
              {jobConfigs.map((config) => (
                <div key={config._id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Job ID: {config.jobId}</h3>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline">
                          {config.stageConfig.length} stages
                        </Badge>
                        <Badge variant="outline">
                          {config.flow.length} flow rules
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Job Configuration Details</DialogTitle>
                            <DialogDescription>
                              Configuration for Job ID: {config.jobId}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <h4 className="font-medium mb-2">Stages:</h4>
                              <div className="space-y-2">
                                {config.stageConfig.map((stage) => (
                                  <div key={stage.stage} className="border rounded p-3">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium">{stage.stage}</span>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setEditingStage({
                                          jobId: config.jobId,
                                          stage: stage.stage,
                                          scheduling: stage.scheduling,
                                          next: config.flow.find(f => f.stage === stage.stage)?.next || []
                                        })}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </div>
                                    <div className="text-sm text-muted-foreground mt-1">
                                      Agent: {stage.action.agentId} | 
                                      Scheduling: {stage.scheduling?.type || 'IMMEDIATE'}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteJobConfig(config.jobId)}
                        disabled={deleteJobConfigMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stage Edit Dialog */}
      <Dialog open={!!editingStage} onOpenChange={() => setEditingStage(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Stage Configuration</DialogTitle>
            <DialogDescription>
              Update scheduling and flow for stage: {editingStage?.stage}
            </DialogDescription>
          </DialogHeader>
          {editingStage && (
            <div className="space-y-4">
              <div>
                <Label>Scheduling Type</Label>
                <Select
                  value={editingStage.scheduling?.type || 'IMMEDIATE'}
                  onValueChange={(value: 'IMMEDIATE' | 'BUSINESS_HOURS') =>
                    setEditingStage({
                      ...editingStage,
                      scheduling: {
                        type: value,
                        params: value === 'BUSINESS_HOURS' ? editingStage.scheduling?.params || {} : {}
                      }
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IMMEDIATE">Immediate</SelectItem>
                    <SelectItem value="BUSINESS_HOURS">Business Hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {editingStage.scheduling?.type === 'BUSINESS_HOURS' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Start Hour</Label>
                    <Input
                      type="number"
                      min="0"
                      max="23"
                      value={editingStage.scheduling.params?.startHour || 9}
                      onChange={(e) =>
                        setEditingStage({
                          ...editingStage,
                          scheduling: {
                            ...editingStage.scheduling!,
                            params: {
                              ...editingStage.scheduling!.params,
                              startHour: parseInt(e.target.value)
                            }
                          }
                        })
                      }
                    />
                  </div>
                  <div>
                    <Label>End Hour</Label>
                    <Input
                      type="number"
                      min="0"
                      max="23"
                      value={editingStage.scheduling.params?.endHour || 17}
                      onChange={(e) =>
                        setEditingStage({
                          ...editingStage,
                          scheduling: {
                            ...editingStage.scheduling!,
                            params: {
                              ...editingStage.scheduling!.params,
                              endHour: parseInt(e.target.value)
                            }
                          }
                        })
                      }
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setEditingStage(null)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUpdateStage}
                  disabled={updateStageMutation.isPending}
                >
                  {updateStageMutation.isPending ? 'Updating...' : 'Update Stage'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
