import { useState } from 'react'
import { Plus } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DataTable } from '@/components/tables/DataTable'
import { RecruiterForm } from '@/components/forms/RecruiterForm'
import { RecruiterDetail } from './RecruiterDetail'
import { useRecruiters, useCreateRecruiter, useUpdateRecruiter, useDeleteRecruiter } from '@/hooks/useRecruiters'
import { formatDate } from '@/lib/utils'

// Temporary type definitions until import issues are fixed
interface IRecruiter {
  _id: string;
  name: string;
  email: string;
  organization: string;
  password: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

interface RecruiterFormData {
  name: string;
  email: string;
  organization: string;
  password: string;
}

export function RecruiterList() {
  const { data: recruiters, isLoading } = useRecruiters()
  const createMutation = useCreateRecruiter()
  const updateMutation = useUpdateRecruiter()
  const deleteMutation = useDeleteRecruiter()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingRecruiter, setEditingRecruiter] = useState<IRecruiter | null>(null)
  const [viewingRecruiter, setViewingRecruiter] = useState<IRecruiter | null>(null)

  const handleCreate = async (data: RecruiterFormData) => {
    await createMutation.mutateAsync(data)
    setIsCreateDialogOpen(false)
  }

  const handleUpdate = async (data: RecruiterFormData) => {
    if (editingRecruiter) {
      await updateMutation.mutateAsync({ id: editingRecruiter._id, data })
      setEditingRecruiter(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this recruiter?')) {
      await deleteMutation.mutateAsync(id)
    }
  }

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    {
      key: 'organization',
      label: 'Organization',
      sortable: true,
      render: (value: string) => {
        // For now, just show the organization ID. Later we can join with organization data
        return `Org ${value}`
      }
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      render: (value: Date | string) => formatDate(value)
    },
  ]

  if (viewingRecruiter) {
    return (
      <RecruiterDetail
        recruiter={viewingRecruiter}
        onBack={() => setViewingRecruiter(null)}
        onEdit={(recruiter: IRecruiter) => {
          setViewingRecruiter(null)
          setEditingRecruiter(recruiter)
        }}
        onDelete={handleDelete}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex justify-end">
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Recruiter
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Recruiter</DialogTitle>
              </DialogHeader>
              <RecruiterForm
                mode="create"
                onSubmit={handleCreate}
                onCancel={() => setIsCreateDialogOpen(false)}
                isLoading={createMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <DataTable
        data={recruiters || []}
        columns={columns}
        loading={isLoading}
        onEdit={(id) => {
          const recruiter = recruiters?.find(r => r._id === id)
          if (recruiter) setEditingRecruiter(recruiter)
        }}
        onDelete={handleDelete}
        onView={(id) => {
          const recruiter = recruiters?.find(r => r._id === id)
          if (recruiter) setViewingRecruiter(recruiter)
        }}
      />

      {/* Edit Dialog */}
      <Dialog open={!!editingRecruiter} onOpenChange={() => setEditingRecruiter(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Recruiter</DialogTitle>
          </DialogHeader>
          {editingRecruiter && (
            <RecruiterForm
              mode="edit"
              initialData={editingRecruiter}
              onSubmit={handleUpdate}
              onCancel={() => setEditingRecruiter(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}