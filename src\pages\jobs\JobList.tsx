import { useState } from 'react'
import { Plus, Briefcase, MapPin, Clock, DollarSign, Play, Square, Settings } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { DataTable } from '@/components/tables/DataTable'
import { JobForm } from '@/components/forms/JobForm'
import { JobDetail } from './JobDetail'
import { JobSearchFilter } from '@/components/search/JobSearchFilter'
import { useJobs, useCreateJob, useUpdateJob, useDeleteJob, useStartJobWorkflow, useStopJobWorkflow, type IJob, type JobFormData, WorkflowStatusEnum } from '@/hooks/useJobs'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

export function JobList() {
  const { data: jobs, isLoading } = useJobs()
  const { user } = useAuth()
  const createMutation = useCreateJob()
  const updateMutation = useUpdateJob()
  const deleteMutation = useDeleteJob()
  const startWorkflowMutation = useStartJobWorkflow()
  const stopWorkflowMutation = useStopJobWorkflow()
  const { toast } = useToast()

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingJob, setEditingJob] = useState<IJob | null>(null)
  const [viewingJob, setViewingJob] = useState<IJob | null>(null)
  const [filteredJobs, setFilteredJobs] = useState<IJob[] | null>(null)

  // Use filtered jobs if available, otherwise use all jobs
  const displayJobs = filteredJobs || (Array.isArray(jobs) ? jobs : []) || []

  const handleCreate = async (data: JobFormData) => {
    if (!user?._id) {
      throw new Error('User not authenticated')
    }

    // Add the current user's ID as createdBy and organization
    const jobData = {
      ...data,
      createdBy: user._id,
      organization: user.organization // Ensure organization is set
    }

    await createMutation.mutateAsync(jobData)
    setIsCreateDialogOpen(false)
  }

  const handleUpdate = async (data: JobFormData) => {
    if (editingJob) {
      await updateMutation.mutateAsync({ id: editingJob._id, data })
      setEditingJob(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this job?')) {
      await deleteMutation.mutateAsync(id)
    }
  }

  const handleStartWorkflow = async (id: string) => {
    if (window.confirm('Are you sure you want to start the workflow for this job?')) {
      await startWorkflowMutation.mutateAsync(id)
    }
  }

  const handleStopWorkflow = async (id: string) => {
    if (window.confirm('Are you sure you want to stop the workflow for this job?')) {
      await stopWorkflowMutation.mutateAsync(id)
    }
  }

  const handleCopyApplicationLink = async (id: string) => {
    const job = displayJobs.find(j => j._id === id)
    if (!job) return

    // Create the public application URL
    const applicationUrl = `${window.location.origin}/apply/${id}`

    try {
      await navigator.clipboard.writeText(applicationUrl)
      toast({
        title: "Application Link Copied",
        description: `Public application link for "${job.title}" has been copied to clipboard.`,
      })
    } catch (error) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea')
      textArea.value = applicationUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)

      toast({
        title: "Application Link Copied",
        description: `Public application link for "${job.title}" has been copied to clipboard.`,
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      case 'closed': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getWorkflowStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800'
      case 'configured': return 'bg-blue-100 text-blue-800'
      case 'not_configured': return 'bg-gray-100 text-gray-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      case 'stopped': return 'bg-orange-100 text-orange-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatWorkflowStatus = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getJobTypeColor = (jobType: string) => {
    switch (jobType) {
      case 'full_time': return 'bg-blue-100 text-blue-800'
      case 'part_time': return 'bg-purple-100 text-purple-800'
      case 'contract': return 'bg-orange-100 text-orange-800'
      case 'internship': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatJobType = (jobType: string) => {
    return jobType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const formatSalary = (salaryRange?: { min: number; max: number; currency: string; period: string }) => {
    if (!salaryRange || !salaryRange.min || !salaryRange.max) return 'Not specified'
    const { min, max, currency, period } = salaryRange
    return `${currency} ${min.toLocaleString()} - ${max.toLocaleString()} / ${period}`
  }

  const columns = [
    { 
      key: 'title', 
      label: 'Job Title', 
      sortable: true,
      render: (value: string, row: IJob) => (
        <div className="flex items-center space-x-2">
          <Briefcase className="h-4 w-4 text-gray-400" />
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-gray-500">{row.department}</div>
          </div>
        </div>
      )
    },
    { 
      key: 'location', 
      label: 'Location', 
      sortable: true,
      render: (value: string, row: IJob) => (
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-gray-400" />
          <div>
            <div>{value}</div>
            <div className="text-sm text-gray-500 capitalize">{row.workLocation}</div>
          </div>
        </div>
      )
    },
    { 
      key: 'jobType', 
      label: 'Type', 
      sortable: true,
      render: (value: string) => (
        <Badge className={getJobTypeColor(value)}>
          {formatJobType(value)}
        </Badge>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => (
        <Badge className={getStatusColor(value)}>
          {value.charAt(0).toUpperCase() + value.slice(1)}
        </Badge>
      )
    },
    {
      key: 'workflowStatus',
      label: 'Workflow',
      sortable: true,
      render: (value: string | undefined, row: IJob) => {
        const workflowStatus = value || 'not_configured'
        return (
          <div className="flex items-center space-x-2">
            <Badge className={getWorkflowStatusColor(workflowStatus)}>
              {formatWorkflowStatus(workflowStatus)}
            </Badge>
            {workflowStatus === 'configured' && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStartWorkflow(row._id)}
                disabled={startWorkflowMutation.isPending}
              >
                <Play className="h-3 w-3" />
              </Button>
            )}
            {workflowStatus === 'running' && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStopWorkflow(row._id)}
                disabled={stopWorkflowMutation.isPending}
              >
                <Square className="h-3 w-3" />
              </Button>
            )}
          </div>
        )
      }
    },
    { 
      key: 'salaryRange', 
      label: 'Salary', 
      sortable: false,
      render: (value: any) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="h-4 w-4 text-gray-400" />
          <span className="text-sm">{formatSalary(value)}</span>
        </div>
      )
    },
    { 
      key: 'openings', 
      label: 'Openings', 
      sortable: true,
      render: (value: number) => (
        <div className="text-center">
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    { 
      key: 'createdAt', 
      label: 'Posted', 
      sortable: true,
      render: (value: Date | string) => (
        <div className="flex items-center space-x-1">
          <Clock className="h-4 w-4 text-gray-400" />
          <span className="text-sm">{formatDate(value)}</span>
        </div>
      )
    },
  ]

  if (viewingJob) {
    return (
      <JobDetail
        job={viewingJob}
        onBack={() => setViewingJob(null)}
        onEdit={(job: IJob) => {
          setViewingJob(null)
          setEditingJob(job)
        }}
        onDelete={handleDelete}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <JobSearchFilter
        onResultsChange={setFilteredJobs}
        className="bg-white p-4 rounded-lg shadow-sm"
      />

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">
                {filteredJobs ? 'Filtered' : 'Total'} Jobs
              </p>
              <p className="text-xl font-bold">{displayJobs.length}</p>
              {filteredJobs && (
                <p className="text-xs text-muted-foreground">
                  of {jobs && Array.isArray(jobs) ? jobs.length : 0} total
                </p>
              )}
            </div>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Active Jobs</p>
              <p className="text-xl font-bold text-green-600">
                {jobs && Array.isArray(jobs) ? jobs.filter(job => job.status === 'active').length : 0}
              </p>
            </div>
            <Briefcase className="h-4 w-4 text-green-600" />
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Draft Jobs</p>
              <p className="text-xl font-bold text-gray-600">
                {jobs && Array.isArray(jobs) ? jobs.filter(job => job.status === 'draft').length : 0}
              </p>
            </div>
            <Briefcase className="h-4 w-4 text-gray-600" />
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm">
          <CardContent className="flex items-center justify-between px-3 py-0">
            <div>
              <p className="text-xs font-medium text-muted-foreground">Total Openings</p>
              <p className="text-xl font-bold text-blue-600">
                {jobs && Array.isArray(jobs) ? jobs.reduce((sum, job) => sum + job.openings, 0) : 0}
              </p>
            </div>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardContent>
        </Card>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Card className="border-2 border-dashed border-gray-300 hover:border-primary hover:bg-gray-50 transition-colors cursor-pointer">
              <CardContent className="flex items-center justify-center px-3 py-0 h-full">
                <div className="text-center">
                  <Plus className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                  <p className="text-sm font-medium text-gray-600">Post New Job</p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Post New Job</DialogTitle>
            </DialogHeader>
            <JobForm
              mode="create"
              onSubmit={handleCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      <DataTable
        data={displayJobs}
        columns={columns}
        loading={isLoading}
        onEdit={(id) => {
          const job = displayJobs.find(j => j._id === id)
          if (job) setEditingJob(job)
        }}
        onDelete={handleDelete}
        onView={(id) => {
          const job = displayJobs.find(j => j._id === id)
          if (job) setViewingJob(job)
        }}
        onCopyApplicationLink={handleCopyApplicationLink}
      />

      {/* Edit Dialog */}
      <Dialog open={!!editingJob} onOpenChange={() => setEditingJob(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Job</DialogTitle>
          </DialogHeader>
          {editingJob && (
            <JobForm
              mode="edit"
              initialData={editingJob}
              onSubmit={handleUpdate}
              onCancel={() => setEditingJob(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
