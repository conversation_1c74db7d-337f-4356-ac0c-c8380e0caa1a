import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'

// Temporary type definitions until import issues are fixed
const JobType = {
  FULL_TIME: "full_time",
  PART_TIME: "part_time",
  CONTRACT: "contract",
  INTERNSHIP: "internship"
} as const

type JobType = typeof JobType[keyof typeof JobType]

const WorkLocation = {
  REMOTE: "remote",
  ONSITE: "onsite",
  HYBRID: "hybrid"
} as const

type WorkLocation = typeof WorkLocation[keyof typeof WorkLocation]

const ExperienceLevel = {
  ENTRY: "entry",
  MID: "mid",
  SENIOR: "senior",
  LEAD: "lead",
  EXECUTIVE: "executive"
} as const

type ExperienceLevel = typeof ExperienceLevel[keyof typeof ExperienceLevel]

const JobStatus = {
  DRAFT: "draft",
  ACTIVE: "active",
  PAUSED: "paused",
  CLOSED: "closed",
  CANCELLED: "cancelled"
} as const

type JobStatus = typeof JobStatus[keyof typeof JobStatus]

const WorkflowStatus = {
  NOT_CONFIGURED: "not_configured",
  CONFIGURED: "configured",
  RUNNING: "running",
  PAUSED: "paused",
  STOPPED: "stopped",
  ERROR: "error"
} as const

type WorkflowStatus = typeof WorkflowStatus[keyof typeof WorkflowStatus]

interface RequiredSkill {
  name: string;
  level: "required" | "preferred" | "nice_to_have";
  yearsRequired?: number;
}

interface SalaryRange {
  min: number;
  max: number;
  currency: string;
  period: "hourly" | "monthly" | "yearly";
}

interface IJob {
  _id: string;
  title: string;
  description: string;
  department: string;
  location: string;
  jobType: JobType;
  workLocation: WorkLocation;
  experienceLevel: ExperienceLevel;
  requiredSkills: RequiredSkill[];
  qualifications: string[];
  responsibilities: string[];
  salaryRange?: SalaryRange;
  status: JobStatus;
  openings: number;
  applicationDeadline?: Date | string;
  startDate?: Date | string;
  postedDate: Date | string;
  // Workflow tracking fields (optional until backend is updated)
  workflowStatus?: WorkflowStatus;
  workflowId?: string;
  workflowStartedAt?: Date | string;
  workflowStoppedAt?: Date | string;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy: string; // Reference to Recruiter
  organization: string; // Reference to Organization
}

interface JobFormData {
  title: string;
  description: string;
  department: string;
  location: string;
  jobType: JobType;
  workLocation: WorkLocation;
  experienceLevel: ExperienceLevel;
  requiredSkills: RequiredSkill[];
  qualifications: string[];
  responsibilities: string[];
  salaryRange?: SalaryRange;
  status: JobStatus;
  openings: number;
  applicationDeadline?: Date;
  startDate?: Date;
  organization: string;
  createdBy?: string; // Optional for form, will be added automatically
}

export function useJobs() {
  return useQuery({
    queryKey: ['jobs'],
    queryFn: () => api.get<IJob[]>('/job'),
  })
}

export function useJob(id: string) {
  return useQuery({
    queryKey: ['jobs', id],
    queryFn: () => api.get<IJob>(`/jobs/${id}`), // Note: This uses /jobs/:jobId for authenticated access
    enabled: !!id,
  })
}

export function useCreateJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: JobFormData) =>
      api.post<IJob>('/job', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
    },
  })
}

export function useUpdateJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: JobFormData }) =>
      api.put<IJob>(`/job/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
    },
  })
}

export function useDeleteJob() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => api.delete(`/job/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
    },
  })
}

// Get candidates for a specific job (from job endpoint)
export function useJobCandidatesFromJob(jobId: string) {
  return useQuery({
    queryKey: ['jobs', 'jobs', jobId, 'candidates'],
    queryFn: () => api.get<Array<{
      _id: string;
      name: string;
      email: string;
      phone: string;
      stage: string;
      status: string;
      appliedDate: string;
    }>>(`/job/jobs/${jobId}/candidates`),
    enabled: !!jobId,
  })
}

// Get job analytics
export function useJobAnalytics() {
  return useQuery({
    queryKey: ['jobs', 'analytics'],
    queryFn: () => api.get<{
      totalJobs: number;
      jobsByStatus: Record<string, number>;
      jobsByDepartment: Record<string, number>;
    }>('/job/analytics'),
  })
}

// Get external job candidates (from external ATS)
export function useExternalJobCandidates(jobId: string) {
  return useQuery({
    queryKey: ['jobs', 'external', jobId, 'candidates'],
    queryFn: () => api.get<Array<{
      id: string;
      name: string;
      email: string;
      phone: string;
      resumeUrl: string;
      source: string;
      appliedDate: string;
    }>>(`/job/external/jobs/${jobId}/candidates`),
    enabled: !!jobId,
  })
}

// Search jobs by title, department, or location
export function useSearchJobs(params: { title?: string; department?: string; location?: string }) {
  return useQuery({
    queryKey: ['jobs', 'search', params],
    queryFn: () => api.get<IJob[]>('/job/search', params),
    enabled: !!(params.title || params.department || params.location),
  })
}

// Filter jobs by status, jobType, experienceLevel, or workLocation
export function useFilterJobs(params: {
  status?: string;
  jobType?: string;
  experienceLevel?: string;
  workLocation?: string;
  department?: string;
}) {
  return useQuery({
    queryKey: ['jobs', 'filter', params],
    queryFn: () => api.get<IJob[]>('/job/filter', params),
    enabled: !!(params.status || params.jobType || params.experienceLevel || params.workLocation || params.department),
  })
}

// Start workflow for a job
export function useStartJobWorkflow() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => api.post(`/job/workflows/${jobId}/start`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
    },
  })
}

// Stop workflow for a job
export function useStopJobWorkflow() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (jobId: string) => api.post(`/job/workflows/${jobId}/stop`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobs'] })
    },
  })
}

// Export types for use in components
export type { IJob, JobFormData, RequiredSkill, SalaryRange, JobType, WorkLocation, ExperienceLevel, JobStatus, WorkflowStatus }
export { JobType as JobTypeEnum, WorkLocation as WorkLocationEnum, ExperienceLevel as ExperienceLevelEnum, JobStatus as JobStatusEnum, WorkflowStatus as WorkflowStatusEnum }
