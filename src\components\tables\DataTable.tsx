import { useState } from 'react'
import { <PERSON><PERSON>ronUp, ChevronDown, Edit, Trash2, Eye, MoreHorizontal, Play, Link } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'

interface Column {
  key: string
  label: string
  sortable?: boolean
  render?: (value: any, row: any) => React.ReactNode
}

interface DataTableProps {
  data: any[]
  columns: Column[]
  loading?: boolean
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
  onView?: (id: string) => void
  onStart?: (id: string) => void
  onCopyApplicationLink?: (id: string) => void
}

export function DataTable({
  data,
  columns,
  loading = false,
  onEdit,
  onDelete,
  onView,
  onStart,
  onCopyApplicationLink
}: DataTableProps) {
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(columnKey)
      setSortDirection('asc')
    }
  }

  const sortedData = [...data].sort((a, b) => {
    if (!sortColumn) return 0
    
    const aValue = a[sortColumn]
    const bValue = b[sortColumn]
    
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  if (loading) {
    return (
      <div className="rounded-md border">
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="rounded-md border bg-white overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full min-w-[1200px]">
        <thead>
          <tr className="border-b bg-gray-50">
            {columns.map((column, columnIndex) => (
              <th
                key={column.key || `header-${columnIndex}`}
                className={cn(
                  "px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
                  column.sortable && "cursor-pointer hover:bg-gray-100"
                )}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.label}</span>
                  {column.sortable && (
                    <div className="flex flex-col">
                      <ChevronUp 
                        className={cn(
                          "h-3 w-3",
                          sortColumn === column.key && sortDirection === 'asc' 
                            ? "text-blue-600" 
                            : "text-gray-400"
                        )} 
                      />
                      <ChevronDown 
                        className={cn(
                          "h-3 w-3 -mt-1",
                          sortColumn === column.key && sortDirection === 'desc' 
                            ? "text-blue-600" 
                            : "text-gray-400"
                        )} 
                      />
                    </div>
                  )}
                </div>
              </th>
            ))}
            <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {sortedData.map((row, index) => {
            const rowKey = row._id || row.id || `row-${index}`;
            return (
              <tr key={rowKey} className="hover:bg-gray-50 transition-colors">
                {columns.map((column, columnIndex) => (
                  <td key={`${rowKey}-${column.key || columnIndex}`} className="px-6 py-5 text-sm text-gray-900">
                    {column.render ? column.render(row[column.key], row) : row[column.key]}
                  </td>
                ))}
                <td key={`${rowKey}-actions`} className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium w-40">
                  <div className="flex items-center justify-end space-x-2">
                    {onStart && row.hasConfig && row.isValid && (
                      <Button
                        size="sm"
                        onClick={() => onStart(row._id || row.id)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Play className="mr-1 h-3 w-3" />
                        Start
                      </Button>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onView && (
                          <DropdownMenuItem onClick={() => onView(row._id || row.id)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </DropdownMenuItem>
                        )}
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(row._id || row.id)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onCopyApplicationLink && (
                          <DropdownMenuItem onClick={() => onCopyApplicationLink(row._id || row.id)}>
                            <Link className="mr-2 h-4 w-4" />
                            Copy Application Link
                          </DropdownMenuItem>
                        )}
                        {onDelete && (row.hasConfig === undefined || row.hasConfig === true) && (
                          <DropdownMenuItem
                            onClick={() => onDelete(row._id || row.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
        </table>
      </div>

      {data.length === 0 && (
        <div className="p-8 text-center">
          <p className="text-gray-500">No data available</p>
        </div>
      )}
    </div>
  )
}